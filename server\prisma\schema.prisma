// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  USER
  ADMIN
  SUPERADMIN
}

enum OrderStatus {
  PENDING
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  role      UserRole @default(USER)
  firstName String?
  lastName  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  orders    Order[]

  @@map("users")
}

model Category {
  id          String    @id @default(cuid())
  name        String
  slug        String    @unique
  description String?
  image       String?
  buttonText  String?
  isActive    Boolean   @default(true)
  sortOrder   Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  products    Product[]

  @@map("categories")
}

model Product {
  id              String           @id @default(cuid())
  name            String
  slug            String           @unique
  description     String?
  shortDescription String?
  price           Decimal          @db.Decimal(10, 2)
  comparePrice    Decimal?         @db.Decimal(10, 2)
  sku             String?          @unique
  barcode         String?
  trackQuantity   Boolean          @default(true)
  quantity        Int              @default(0)
  weight          Decimal?         @db.Decimal(8, 3)
  isActive        Boolean          @default(true)
  isFeatured      Boolean          @default(false)
  isTrending      Boolean          @default(false)
  isNewArrival    Boolean          @default(false)
  badgeText       String?
  metaTitle       String?
  metaDescription String?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  categoryId      String
  category        Category         @relation(fields: [categoryId], references: [id])

  images          ProductImage[]
  variants        ProductVariant[]
  accordions      ProductAccordion[]
  orderItems      OrderItem[]

  @@map("products")
}

model ProductImage {
  id        String  @id @default(cuid())
  url       String
  altText   String?
  sortOrder Int     @default(0)

  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductVariant {
  id        String @id @default(cuid())
  name      String
  value     String

  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_variants")
}

model ProductAccordion {
  id        String @id @default(cuid())
  title     String
  content   String
  sortOrder Int    @default(0)

  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_accordions")
}

model Order {
  id            String      @id @default(cuid())
  orderNumber   String      @unique
  status        OrderStatus @default(PENDING)
  totalAmount   Decimal     @db.Decimal(10, 2)
  shippingCost  Decimal     @default(0) @db.Decimal(10, 2)
  taxAmount     Decimal     @default(0) @db.Decimal(10, 2)

  // Customer Information
  customerEmail String
  firstName     String
  lastName      String
  phone         String?

  // Shipping Address
  shippingAddress1 String
  shippingAddress2 String?
  shippingCity     String
  shippingState    String?
  shippingZip      String
  shippingCountry  String

  // Billing Address (optional, can be same as shipping)
  billingAddress1 String?
  billingAddress2 String?
  billingCity     String?
  billingState    String?
  billingZip      String?
  billingCountry  String?

  // Payment Information
  stripePaymentIntentId String?
  paymentStatus         String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId    String?
  user      User?       @relation(fields: [userId], references: [id])
  items     OrderItem[]

  @@map("orders")
}

model OrderItem {
  id       String  @id @default(cuid())
  quantity Int
  price    Decimal @db.Decimal(10, 2)

  orderId   String
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)

  productId String
  product   Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

model CarouselSlide {
  id         String  @id @default(cuid())
  title      String?
  subtitle   String?
  image      String?
  link       String?
  buttonText String?
  isActive   Boolean @default(true)
  sortOrder  Int     @default(0)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@map("carousel_slides")
}

model BenefitCard {
  id          String  @id @default(cuid())
  title       String
  description String
  icon        String?
  image       String?
  isActive    Boolean @default(true)
  sortOrder   Int     @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("benefit_cards")
}

model LimitedTimeOffer {
  id          String    @id @default(cuid())
  badge       String
  title       String
  subtitle    String
  buttonText  String
  buttonLink  String?
  endDate     DateTime?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("limited_time_offers")
}

model Newsletter {
  id        String   @id @default(cuid())
  email     String   @unique
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())

  @@map("newsletter_subscribers")
}

model LegalPage {
  id        String   @id @default(cuid())
  slug      String   @unique
  title     String
  content   String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("legal_pages")
}

model CompanySettings {
  id              String   @id @default(cuid())
  companyName     String
  companyEmail    String
  companyPhone    String?
  companyAddress1 String?
  companyAddress2 String?
  companyCity     String?
  companyState    String?
  companyZip      String?
  companyCountry  String?

  // Social Media
  facebookUrl     String?
  twitterUrl      String?
  instagramUrl    String?
  linkedinUrl     String?

  // SEO
  metaTitle       String?
  metaDescription String?

  // Footer
  copyrightText   String?

  // Newsletter
  newsletterTitle       String?
  newsletterDescription String?

  // Logo
  logoUrl String?
  logoImageStyles Json?
  logoText String?
  logoTextStyles Json?

  // Homepage Design Settings
  navbarDesign Json?
  heroDesign Json?
  benefitCardsDesign Json?
  sectionBackgrounds Json?
  cardsDesign Json?
  typographyDesign Json?
  typographyDarkDesign Json? // Typography for dark backgrounds
  pageDesign Json?
  buttonDesign Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("company_settings")
}

model CategorySlideshow {
  id          String   @id @default(cuid())
  title       String?
  subtitle    String?
  image       String
  link        String?
  isActive    Boolean  @default(true)
  sortOrder   Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("category_slideshows")
}
