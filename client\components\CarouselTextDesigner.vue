<template>
  <div class="space-y-6">
    <!-- Section Header -->
    <div class="border-b pb-4">
      <h3 class="text-xl font-semibold">Carousel Text Design</h3>
      <p class="text-base-content/70 mt-1">
        Customize titles, subtitles, and CTA buttons for carousel slides
      </p>
    </div>

    <!-- Title Design -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h4 class="card-title text-lg">Title Styling</h4>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Font Family -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Font Family</span>
            </label>
            <select
              v-model="localDesign.title.fontFamily"
              class="select select-bordered select-sm"
              @change="updateDesign"
            >
              <option value="Inter">Inter</option>
              <option value="Roboto">Roboto</option>
              <option value="Open Sans">Open Sans</option>
              <option value="Montserrat">Montserrat</option>
              <option value="Poppins">Poppins</option>
              <option value="Playfair Display">Playfair Display</option>
              <option value="Oswald">Oswald</option>
            </select>
          </div>

          <!-- Font Size -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Font Size</span>
            </label>
            <input
              v-model.number="localDesign.title.fontSize"
              type="range"
              min="24"
              max="96"
              class="range range-sm"
              @input="updateDesign"
            />
            <div class="text-xs text-center mt-1">{{ localDesign.title.fontSize }}px</div>
          </div>

          <!-- Font Weight -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Font Weight</span>
            </label>
            <select
              v-model="localDesign.title.fontWeight"
              class="select select-bordered select-sm"
              @change="updateDesign"
            >
              <option value="300">Light</option>
              <option value="400">Normal</option>
              <option value="500">Medium</option>
              <option value="600">Semi Bold</option>
              <option value="700">Bold</option>
              <option value="800">Extra Bold</option>
              <option value="900">Black</option>
            </select>
          </div>

          <!-- Color -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Color</span>
            </label>
            <input
              v-model="localDesign.title.color"
              type="color"
              class="input input-bordered w-full h-10"
              @input="updateDesign"
            />
          </div>
        </div>

        <!-- Preview -->
        <div class="mt-4 p-4 bg-base-200 rounded-lg">
          <h1
            :style="{
              fontFamily: localDesign.title.fontFamily,
              fontSize: `${localDesign.title.fontSize}px`,
              fontWeight: localDesign.title.fontWeight,
              color: localDesign.title.color,
            }"
            class="mb-0"
          >
            Sample Title Text
          </h1>
        </div>
      </div>
    </div>

    <!-- Subtitle Design -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h4 class="card-title text-lg">Subtitle Styling</h4>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Font Family -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Font Family</span>
            </label>
            <select
              v-model="localDesign.subtitle.fontFamily"
              class="select select-bordered select-sm"
              @change="updateDesign"
            >
              <option value="Inter">Inter</option>
              <option value="Roboto">Roboto</option>
              <option value="Open Sans">Open Sans</option>
              <option value="Montserrat">Montserrat</option>
              <option value="Poppins">Poppins</option>
              <option value="Playfair Display">Playfair Display</option>
              <option value="Oswald">Oswald</option>
            </select>
          </div>

          <!-- Font Size -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Font Size</span>
            </label>
            <input
              v-model.number="localDesign.subtitle.fontSize"
              type="range"
              min="12"
              max="48"
              class="range range-sm"
              @input="updateDesign"
            />
            <div class="text-xs text-center mt-1">{{ localDesign.subtitle.fontSize }}px</div>
          </div>

          <!-- Font Weight -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Font Weight</span>
            </label>
            <select
              v-model="localDesign.subtitle.fontWeight"
              class="select select-bordered select-sm"
              @change="updateDesign"
            >
              <option value="300">Light</option>
              <option value="400">Normal</option>
              <option value="500">Medium</option>
              <option value="600">Semi Bold</option>
              <option value="700">Bold</option>
            </select>
          </div>

          <!-- Color -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Color</span>
            </label>
            <input
              v-model="localDesign.subtitle.color"
              type="color"
              class="input input-bordered w-full h-10"
              @input="updateDesign"
            />
          </div>
        </div>

        <!-- Preview -->
        <div class="mt-4 p-4 bg-base-200 rounded-lg">
          <p
            :style="{
              fontFamily: localDesign.subtitle.fontFamily,
              fontSize: `${localDesign.subtitle.fontSize}px`,
              fontWeight: localDesign.subtitle.fontWeight,
              color: localDesign.subtitle.color,
            }"
            class="mb-0"
          >
            Sample subtitle text that describes the content
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  design: {
    type: Object,
    required: true,
  },
});

// Emits
const emit = defineEmits(["update:design", "update"]);

// Default design settings based on DaisyUI defaults
const getDefaultDesign = () => ({
  title: {
    fontFamily: "Inter",
    fontSize: 48,
    fontWeight: "700",
    color: "#ffffff", // Default white for overlay text
  },
  subtitle: {
    fontFamily: "Inter", 
    fontSize: 18,
    fontWeight: "400",
    color: "#ffffff", // Default white for overlay text
  },
});

// Local state
const localDesign = ref({ ...getDefaultDesign(), ...(props.design || {}) });

// Methods
const updateDesign = () => {
  emit("update:design", { ...localDesign.value });
  emit("update");
};

// Watch for prop changes
watch(
  () => props.design,
  (newDesign) => {
    localDesign.value = { ...getDefaultDesign(), ...newDesign };
  },
  { deep: true }
);
</script>
