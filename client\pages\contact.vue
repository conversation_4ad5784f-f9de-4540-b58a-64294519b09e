<template>
  <div class="min-h-screen bg-base-100">
    <AppNavbar />
    
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Breadcrumbs -->
      <div class="breadcrumbs text-sm mb-8">
        <ul>
          <li><NuxtLink to="/">Home</NuxtLink></li>
          <li>Contact Us</li>
        </ul>
      </div>

      <!-- Header -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold mb-4">Contact Us</h1>
        <p class="text-xl text-base-content/70 max-w-2xl mx-auto">
          We're here to help! Get in touch with us for any questions, concerns, or feedback.
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- Contact Form -->
        <div class="card bg-base-200 shadow-xl">
          <div class="card-body">
            <h2 class="card-title mb-6">Send us a Message</h2>
            
            <form @submit.prevent="handleSubmit" class="space-y-4">
              <!-- Name -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Full Name *</span>
                </label>
                <input 
                  v-model="form.name"
                  type="text" 
                  placeholder="Enter your full name"
                  class="input input-bordered w-full"
                  :class="{ 'input-error': errors.name }"
                  required
                />
                <label v-if="errors.name" class="label">
                  <span class="label-text-alt text-error">{{ errors.name }}</span>
                </label>
              </div>

              <!-- Email -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Email Address *</span>
                </label>
                <input 
                  v-model="form.email"
                  type="email" 
                  placeholder="Enter your email address"
                  class="input input-bordered w-full"
                  :class="{ 'input-error': errors.email }"
                  required
                />
                <label v-if="errors.email" class="label">
                  <span class="label-text-alt text-error">{{ errors.email }}</span>
                </label>
              </div>

              <!-- Subject -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Subject *</span>
                </label>
                <select 
                  v-model="form.subject"
                  class="select select-bordered w-full"
                  :class="{ 'select-error': errors.subject }"
                  required
                >
                  <option value="">Select a subject</option>
                  <option value="general">General Inquiry</option>
                  <option value="order">Order Support</option>
                  <option value="product">Product Question</option>
                  <option value="technical">Technical Support</option>
                  <option value="billing">Billing Issue</option>
                  <option value="feedback">Feedback</option>
                </select>
                <label v-if="errors.subject" class="label">
                  <span class="label-text-alt text-error">{{ errors.subject }}</span>
                </label>
              </div>

              <!-- Message -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Message *</span>
                </label>
                <textarea 
                  v-model="form.message"
                  placeholder="Enter your message"
                  class="textarea textarea-bordered h-32 w-full"
                  :class="{ 'textarea-error': errors.message }"
                  required
                ></textarea>
                <label v-if="errors.message" class="label">
                  <span class="label-text-alt text-error">{{ errors.message }}</span>
                </label>
              </div>

              <!-- Error/Success Messages -->
              <div v-if="error" class="alert alert-error">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{{ error }}</span>
              </div>

              <div v-if="success" class="alert alert-success">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{{ success }}</span>
              </div>

              <!-- Submit Button -->
              <div class="form-control">
                <button 
                  type="submit" 
                  class="btn btn-primary w-full"
                  :class="{ 'loading': loading }"
                  :disabled="loading"
                >
                  <span v-if="!loading">Send Message</span>
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Contact Information -->
        <div class="space-y-6">
          <!-- Contact Details -->
          <div class="card bg-base-200 shadow-xl">
            <div class="card-body">
              <h2 class="card-title mb-4">Get in Touch</h2>
              
              <div class="space-y-4">
                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary-content" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <div class="font-medium">Email</div>
                    <div class="text-base-content/70"><EMAIL></div>
                  </div>
                </div>

                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 bg-secondary rounded-full flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-secondary-content" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <div>
                    <div class="font-medium">Phone</div>
                    <div class="text-base-content/70">+****************</div>
                  </div>
                </div>

                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 bg-accent rounded-full flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent-content" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <div class="font-medium">Address</div>
                    <div class="text-base-content/70">123 Commerce St, City, State 12345</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Business Hours -->
          <div class="card bg-base-200 shadow-xl">
            <div class="card-body">
              <h2 class="card-title mb-4">Business Hours</h2>
              
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span>Monday - Friday</span>
                  <span class="text-base-content/70">9:00 AM - 6:00 PM</span>
                </div>
                <div class="flex justify-between">
                  <span>Saturday</span>
                  <span class="text-base-content/70">10:00 AM - 4:00 PM</span>
                </div>
                <div class="flex justify-between">
                  <span>Sunday</span>
                  <span class="text-base-content/70">Closed</span>
                </div>
              </div>
              
              <div class="divider"></div>
              
              <div class="text-sm text-base-content/70">
                <strong>Response Time:</strong> We typically respond to inquiries within 24 hours during business days.
              </div>
            </div>
          </div>

          <!-- FAQ Link -->
          <div class="card bg-gradient-to-r from-primary to-secondary text-primary-content shadow-xl">
            <div class="card-body text-center">
              <h3 class="card-title justify-center">Need Quick Answers?</h3>
              <p class="mb-4">Check out our FAQ section for instant answers to common questions.</p>
              <div class="card-actions justify-center">
                <button class="btn btn-accent">View FAQ</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <AppFooter />
    <CartDrawer />
  </div>
</template>

<script setup>
// Form state
const form = reactive({
  name: '',
  email: '',
  subject: '',
  message: ''
})

const loading = ref(false)
const error = ref('')
const success = ref('')
const errors = reactive({
  name: '',
  email: '',
  subject: '',
  message: ''
})

// Clear errors when form changes
watch(() => form, () => {
  Object.keys(errors).forEach(key => {
    errors[key] = ''
  })
  error.value = ''
  success.value = ''
}, { deep: true })

// Handle form submission
const handleSubmit = async () => {
  // Reset errors
  Object.keys(errors).forEach(key => {
    errors[key] = ''
  })
  error.value = ''
  success.value = ''

  // Basic validation
  if (!form.name.trim()) {
    errors.name = 'Name is required'
    return
  }

  if (!form.email.trim()) {
    errors.email = 'Email is required'
    return
  }

  if (!form.subject) {
    errors.subject = 'Subject is required'
    return
  }

  if (!form.message.trim()) {
    errors.message = 'Message is required'
    return
  }

  loading.value = true

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    success.value = 'Thank you for your message! We\'ll get back to you within 24 hours.'
    
    // Reset form
    Object.keys(form).forEach(key => {
      form[key] = ''
    })
  } catch (err) {
    error.value = 'Failed to send message. Please try again.'
  } finally {
    loading.value = false
  }

  // Clear messages after 5 seconds
  setTimeout(() => {
    error.value = ''
    success.value = ''
  }, 5000)
}

// Meta tags
useHead({
  title: 'Contact Us - Multi Store',
  meta: [
    { name: 'description', content: 'Get in touch with Multi Store for any questions, concerns, or feedback. We\'re here to help!' }
  ]
})
</script>
