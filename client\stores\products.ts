import { defineStore } from "pinia";

interface Product {
  id: string;
  name: string;
  slug: string;
  description?: string;
  price: string;
  imageUrl?: string;
  isFeatured: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Pagination {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface ProductFilters {
  search: string;
  categoryId: string | null;
  sortBy: "newest" | "price-low" | "price-high" | "name";
  priceRange: [number, number];
}

interface ProductsState {
  products: Product[];
  trendingProducts: Product[];
  newArrivals: Product[];
  currentProduct: Product | null;
  relatedProducts: Product[];
  loading: boolean;
  pagination: Pagination;
  filters: ProductFilters;
}

interface FetchProductsParams {
  page?: number;
  limit?: number;
  search?: string;
  categoryId?: string;
  sortBy?: string;
  [key: string]: any;
}

export const useProductsStore = defineStore("products", {
  state: (): ProductsState => ({
    products: [],
    trendingProducts: [],
    newArrivals: [],
    currentProduct: null,
    relatedProducts: [],
    loading: false,
    pagination: {
      currentPage: 1,
      totalPages: 1,
      totalCount: 0,
      hasNextPage: false,
      hasPrevPage: false,
    },
    filters: {
      search: "",
      categoryId: null,
      sortBy: "newest",
      priceRange: [0, 1000],
    },
  }),

  getters: {
    filteredProducts: (state): Product[] => {
      let filtered = [...state.products];

      // Apply search filter
      if (state.filters.search) {
        const searchTerm = state.filters.search.toLowerCase();
        filtered = filtered.filter(
          (product) =>
            product.name.toLowerCase().includes(searchTerm) ||
            product.description?.toLowerCase().includes(searchTerm)
        );
      }

      // Apply price range filter
      filtered = filtered.filter((product) => {
        const price = parseFloat(product.price);
        return (
          price >= state.filters.priceRange[0] &&
          price <= state.filters.priceRange[1]
        );
      });

      // Apply sorting
      switch (state.filters.sortBy) {
        case "price-low":
          filtered.sort((a, b) => parseFloat(a.price) - parseFloat(b.price));
          break;
        case "price-high":
          filtered.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));
          break;
        case "name":
          filtered.sort((a, b) => a.name.localeCompare(b.name));
          break;
        case "newest":
        default:
          filtered.sort(
            (a, b) =>
              new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
          break;
      }

      return filtered;
    },

    featuredProducts: (state): Product[] =>
      state.products.filter((product) => product.isFeatured),

    productById:
      (state) =>
      (id: string): Product | undefined =>
        state.products.find((product) => product.id === id),

    productBySlug:
      (state) =>
      (slug: string): Product | undefined =>
        state.products.find((product) => product.slug === slug),
  },

  actions: {
    async fetchProducts(
      params: FetchProductsParams = {}
    ): Promise<{ products: Product[]; pagination: Pagination }> {
      this.loading = true;
      try {
        const api = useApi();
        const response = await api.get<{
          products: Product[];
          pagination: Pagination;
        }>("/products", {
          params: {
            page: params.page || 1,
            limit: params.limit || 12,
            ...params,
          },
        });

        if (params.page === 1 || !params.page) {
          this.products = response.products;
        } else {
          // Append for pagination
          this.products.push(...response.products);
        }

        this.pagination = response.pagination;
        return response;
      } catch (error: unknown) {
        console.error("Fetch products error:", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchTrendingProducts(): Promise<{ products: Product[] }> {
      try {
        const api = useApi();
        const response = await api.get<{ products: Product[] }>(
          "/public/products/trending"
        );
        this.trendingProducts = response.products;
        return response;
      } catch (error: unknown) {
        console.error("Fetch trending products error:", error);
        throw error;
      }
    },

    async fetchNewArrivals(): Promise<{ products: Product[] }> {
      try {
        const api = useApi();
        const response = await api.get<{ products: Product[] }>(
          "/public/products/new-arrivals"
        );
        this.newArrivals = response.products;
        return response;
      } catch (error: unknown) {
        console.error("Fetch new arrivals error:", error);
        throw error;
      }
    },

    async fetchProductBySlug(
      slug: string
    ): Promise<{ product: Product; relatedProducts: Product[] }> {
      this.loading = true;
      try {
        const api = useApi();
        const response = await api.get<{
          product: Product;
          relatedProducts: Product[];
        }>(`/public/products/${slug}`);
        this.currentProduct = response.product;
        this.relatedProducts = response.relatedProducts;
        return response;
      } catch (error: unknown) {
        console.error("Fetch product error:", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchCategoryProducts(
      categorySlug: string,
      params: FetchProductsParams = {}
    ): Promise<{ products: Product[]; pagination: Pagination }> {
      this.loading = true;
      try {
        const api = useApi();
        const response = await api.get<{
          products: Product[];
          pagination: Pagination;
        }>(`/public/categories/${categorySlug}/products`, {
          params: {
            page: params.page || 1,
            limit: params.limit || 9,
            ...params,
          },
        });

        if (params.page === 1 || !params.page) {
          this.products = response.products;
        } else {
          // Append for lazy loading
          this.products.push(...response.products);
        }

        this.pagination = response.pagination;
        return response;
      } catch (error: unknown) {
        console.error("Fetch category products error:", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    updateFilters(newFilters: Partial<ProductFilters>): void {
      this.filters = { ...this.filters, ...newFilters };
    },

    clearFilters(): void {
      this.filters = {
        search: "",
        categoryId: null,
        sortBy: "newest",
        priceRange: [0, 1000],
      };
    },

    // Search products
    async searchProducts(
      searchTerm: string,
      params: FetchProductsParams = {}
    ): Promise<{ products: Product[]; pagination: Pagination }> {
      return await this.fetchProducts({
        search: searchTerm,
        ...params,
      });
    },

    // Load more products for infinite scroll
    async loadMoreProducts(): Promise<
      { products: Product[]; pagination: Pagination } | undefined
    > {
      if (this.pagination.hasNextPage && !this.loading) {
        return await this.fetchProducts({
          page: this.pagination.currentPage + 1,
          ...this.filters,
          categoryId: this.filters.categoryId || undefined,
        });
      }
    },

    // Clear current product data
    clearCurrentProduct(): void {
      this.currentProduct = null;
      this.relatedProducts = [];
    },
  },
});
