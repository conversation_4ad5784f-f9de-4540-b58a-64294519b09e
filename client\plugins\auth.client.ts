export default defineNuxtPlugin({
  name: "auth-init",
  dependsOn: ["pinia"],
  async setup() {
    if (import.meta.client) {
      // Use nextTick to ensure all plugins are loaded
      await nextTick();

      try {
        const authStore = useAuthStore();

        // Initialize auth state from persisted data
        if (authStore.token) {
          authStore.initializeAuth();
        }
      } catch (error: unknown) {
        console.error("Auth plugin initialization error:", error);
      }
    }
  },
});
