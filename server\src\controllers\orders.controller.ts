import { Request, Response, NextFunction } from "express";
import { PrismaClient } from "@prisma/client";
import { createError } from "../utils/wrapController";

const prisma = new PrismaClient();

// Request interfaces
export interface CreateOrderRequest {
  items: Array<{
    productId: string;
    quantity: number;
  }>;
  customerEmail: string;
  firstName: string;
  lastName: string;
  phone?: string;
  shippingAddress1: string;
  shippingAddress2?: string;
  shippingCity: string;
  shippingState?: string;
  shippingZip: string;
  shippingCountry: string;
  billingAddress1?: string;
  billingAddress2?: string;
  billingCity?: string;
  billingState?: string;
  billingZip?: string;
  billingCountry?: string;
}

export interface OrdersQuery {
  page?: string;
  limit?: string;
}

/**
 * POST /api/orders
 * Create a new order
 */
export const createOrder = async (
  req: Request<{}, {}, CreateOrderRequest>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const {
    items,
    customerEmail,
    firstName,
    lastName,
    phone,
    shippingAddress1,
    shippingAddress2,
    shippingCity,
    shippingState,
    shippingZip,
    shippingCountry,
    billingAddress1,
    billingAddress2,
    billingCity,
    billingState,
    billingZip,
    billingCountry,
  } = req.body;

  try {
    // Validate required fields
    if (
      !items ||
      !items.length ||
      !customerEmail ||
      !firstName ||
      !lastName ||
      !shippingAddress1 ||
      !shippingCity ||
      !shippingZip ||
      !shippingCountry
    ) {
      next(createError(400, "Missing required fields"));
      return;
    }

    // Calculate total amount
    let totalAmount = 0;
    const orderItems = [];

    for (const item of items) {
      const product = await prisma.product.findUnique({
        where: { id: item.productId, isActive: true },
      });

      if (!product) {
        next(createError(400, `Product ${item.productId} not found`));
        return;
      }

      const itemTotal = Number(product.price) * item.quantity;
      totalAmount += itemTotal;

      orderItems.push({
        productId: product.id,
        quantity: item.quantity,
        price: product.price,
      });
    }

    // Generate order number
    const orderNumber = `ORD-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)
      .toUpperCase()}`;

    // Create order
    const order = await prisma.order.create({
      data: {
        orderNumber,
        totalAmount,
        customerEmail: customerEmail.toLowerCase(),
        firstName,
        lastName,
        phone,
        shippingAddress1,
        shippingAddress2,
        shippingCity,
        shippingState,
        shippingZip,
        shippingCountry,
        billingAddress1,
        billingAddress2,
        billingCity,
        billingState,
        billingZip,
        billingCountry,
        userId: req.user?.id,
        items: {
          create: orderItems,
        },
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                name: true,
                images: {
                  take: 1,
                  orderBy: { sortOrder: "asc" },
                },
              },
            },
          },
        },
      },
    });

    res.status(201).json({
      message: "Order created successfully",
      order,
    });
  } catch (error) {
    console.error("Create order error:", error);
    next(createError(500, "Failed to create order"));
  }
};

/**
 * GET /api/orders/my-orders
 * Get user's orders
 */
export const getUserOrders = async (
  req: Request<{}, {}, {}, OrdersQuery>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const page = parseInt(req.query.page || "1");
    const limit = parseInt(req.query.limit || "10");
    const skip = (page - 1) * limit;

    const [orders, totalCount] = await Promise.all([
      prisma.order.findMany({
        where: { userId: req.user!.id },
        include: {
          items: {
            include: {
              product: {
                select: {
                  name: true,
                  images: {
                    take: 1,
                    orderBy: { sortOrder: "asc" },
                  },
                },
              },
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.order.count({
        where: { userId: req.user!.id },
      }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    res.json({
      orders,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    console.error("Get user orders error:", error);
    next(createError(500, "Failed to get orders"));
  }
};
