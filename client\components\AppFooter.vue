<!--  client/components/AppFooter.vue -->
<template>
  <footer class="bg-base-200">
    <!-- Main Footer -->
    <div class="container-max section-padding py-16">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- Logo and Description -->
        <div>
          <div class="flex items-center mb-4">
            <div
              v-if="companySettings?.logoUrl"
              :style="logoImageStyle"
              class="mr-2"
            >
              <NuxtImg
                :src="companySettings.logoUrl"
                :alt="companySettings.companyName || 'Store Logo'"
                class="w-full h-full object-contain"
                preset="logo"
              />
            </div>
            <Icon v-else name="heroicons:shopping-bag" class="h-6 w-6 mr-2" />
            <span
              v-if="companySettings?.logoText"
              :style="logoTextStyle"
              class="leading-none"
            >
              {{ companySettings.logoText }}
            </span>
            <!-- Show company name only if no logo image AND no logo text -->
            <span
              v-else-if="!companySettings?.logoUrl"
              class="text-xl font-bold"
            >
              {{ companySettings?.companyName || "Multi Store" }}
            </span>
          </div>
          <p class="text-base-content/70 mb-4">
            {{
              companySettings?.metaDescription ||
              "Your one-stop shop for amazing products at great prices."
            }}
          </p>
          <!-- Social Media Links -->
          <div class="flex gap-2">
            <a
              v-if="companySettings?.facebookUrl"
              :href="companySettings.facebookUrl"
              class="btn btn-ghost btn-sm btn-circle"
            >
              <Icon name="simple-icons:facebook" class="h-4 w-4" />
            </a>
            <a
              v-if="companySettings?.twitterUrl"
              :href="companySettings.twitterUrl"
              class="btn btn-ghost btn-sm btn-circle"
            >
              <Icon name="simple-icons:twitter" class="h-4 w-4" />
            </a>
            <a
              v-if="companySettings?.instagramUrl"
              :href="companySettings.instagramUrl"
              class="btn btn-ghost btn-sm btn-circle"
            >
              <Icon name="simple-icons:instagram" class="h-4 w-4" />
            </a>
            <a
              v-if="companySettings?.linkedinUrl"
              :href="companySettings.linkedinUrl"
              class="btn btn-ghost btn-sm btn-circle"
            >
              <Icon name="simple-icons:linkedin" class="h-4 w-4" />
            </a>
          </div>
        </div>

        <!-- Categories -->
        <div>
          <h3 class="font-semibold mb-4">Categories</h3>
          <ul class="space-y-2">
            <li
              v-for="category in categories?.categories || []"
              :key="category.id"
            >
              <NuxtLink
                :to="`/categories/${category.slug}`"
                class="link link-hover text-base-content/70"
              >
                {{ category.name }}
              </NuxtLink>
            </li>
          </ul>
        </div>

        <!-- Company Info -->
        <div>
          <h3 class="font-semibold mb-4">Company</h3>
          <ul class="space-y-2">
            <li>
              <NuxtLink
                to="/about"
                class="link link-hover text-base-content/70"
              >
                About Us
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/contact"
                class="link link-hover text-base-content/70"
              >
                Contact
              </NuxtLink>
            </li>
          </ul>
        </div>

        <!-- Legal -->
        <div>
          <h3 class="font-semibold mb-4">Legal</h3>
          <ul class="space-y-2">
            <li>
              <NuxtLink
                to="/legal/terms-of-service"
                class="link link-hover text-base-content/70"
              >
                Terms of Service
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/legal/privacy-policy"
                class="link link-hover text-base-content/70"
              >
                Privacy Policy
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/legal/delivery-information"
                class="link link-hover text-base-content/70"
              >
                Delivery Information
              </NuxtLink>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Copyright -->
    <div class="border-t border-base-300">
      <div class="container-max section-padding py-4">
        <div class="text-center text-sm text-base-content/70">
          <p>
            © {{ new Date().getFullYear() }}
            {{ companySettings?.settings?.companyName || "Multi Store" }}. All
            rights reserved.
          </p>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
const contentStore = useContentStore();
const categoriesStore = useCategoriesStore();

// Load company settings if not already loaded
if (!contentStore.companySettings) {
  await contentStore.fetchCompanySettings();
}

// Use store data directly
const companySettings = computed(() => contentStore.companySettings);

const { data: categories, error: categoriesError } = await useAsyncData(
  "footer-categories",
  () => categoriesStore.fetchCategories(),
  {
    // Don't throw errors during SSR, handle gracefully
    default: () => ({ categories: [] }),
  }
);

// Log errors for debugging but don't break the page
if (categoriesError.value) {
  console.warn("Footer: Categories failed to load:", categoriesError.value);
}

// Computed style for logo text
const logoTextStyle = computed(() => {
  if (!companySettings.value?.logoTextStyles) return {};

  const styles = companySettings.value.logoTextStyles;
  return {
    fontFamily: styles.fontFamily,
    fontSize: `${styles.fontSize}px`,
    fontWeight: styles.fontWeight,
    fontStyle: styles.fontStyle,
    color: styles.color,
    marginTop: `${styles.marginTop || 0}px`,
    marginRight: `${styles.marginRight || 0}px`,
    marginBottom: `${styles.marginBottom || 0}px`,
    marginLeft: `${styles.marginLeft || 0}px`,
    paddingTop: `${styles.paddingTop || 0}px`,
    paddingRight: `${styles.paddingRight || 0}px`,
    paddingBottom: `${styles.paddingBottom || 0}px`,
    paddingLeft: `${styles.paddingLeft || 0}px`,
  };
});

// Computed style for logo image
const logoImageStyle = computed(() => {
  if (!companySettings.value?.logoImageStyles)
    return { width: "24px", height: "24px" };

  const styles = companySettings.value.logoImageStyles;
  return {
    width: `${styles.width || 24}px`,
    height: styles.maintainAspectRatio
      ? `${styles.width || 24}px`
      : `${styles.height || 24}px`,
    marginTop: `${styles.marginTop || 0}px`,
    marginRight: `${styles.marginRight || 0}px`,
    marginBottom: `${styles.marginBottom || 0}px`,
    marginLeft: `${styles.marginLeft || 0}px`,
    paddingTop: `${styles.paddingTop || 0}px`,
    paddingRight: `${styles.paddingRight || 0}px`,
    paddingBottom: `${styles.paddingBottom || 0}px`,
    paddingLeft: `${styles.paddingLeft || 0}px`,
  };
});
</script>
