<template>
  <div
    class="w-full"
    :style="{
      paddingTop: (benefitDesign?.padding?.vertical || 0) + 'px',
      paddingBottom: (benefitDesign?.padding?.vertical || 0) + 'px',
      paddingLeft: (benefitDesign?.padding?.horizontal || 0) + 'px',
      paddingRight: (benefitDesign?.padding?.horizontal || 0) + 'px',
    }"
  >
    <div :class="getGridClasses()">
      <div
        v-for="benefit in displayBenefits"
        :key="benefit.id"
        class="card bg-base-100 shadow-xl"
      >
        <div class="card-body text-center">
          <!-- Icon or Image -->
          <div class="mx-auto mb-4">
            <div
              v-if="benefit.iconType === 'emoji' && benefit.icon"
              class="text-4xl"
            >
              {{ benefit.icon }}
            </div>
            <div
              v-else-if="benefit.iconType === 'image' && benefit.image"
              class="flex justify-center"
            >
              <NuxtImg
                :src="benefit.image"
                :alt="benefit.title"
                class="w-16 h-16 object-cover rounded-lg"
              />
            </div>
            <div v-else-if="benefit.icon" class="text-4xl">
              {{ benefit.icon }}
            </div>
            <div v-else class="text-4xl">✨</div>
          </div>

          <!-- Title -->
          <h3 class="card-title justify-center text-lg">{{ benefit.title }}</h3>

          <!-- Description -->
          <p class="text-base-content/70">{{ benefit.description }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const contentStore = useContentStore();

// Get homepage design from store
const homepageDesign = computed(() => contentStore.homepageDesign);
const benefitDesign = computed(() => homepageDesign.value?.benefitCardsDesign);

// Default benefits if none are loaded
const defaultBenefits = [
  {
    id: "default-1",
    title: "Free Shipping",
    description: "Free shipping on orders over $50",
    icon: "🚚",
    iconType: "emoji",
    image: null,
  },
  {
    id: "default-2",
    title: "24/7 Support",
    description: "Round-the-clock customer support",
    icon: "💬",
    iconType: "emoji",
    image: null,
  },
  {
    id: "default-3",
    title: "Easy Returns",
    description: "30-day hassle-free returns",
    icon: "↩️",
    iconType: "emoji",
    image: null,
  },
  {
    id: "default-4",
    title: "Secure Payment",
    description: "Your payment information is safe",
    icon: "🔒",
    iconType: "emoji",
    image: null,
  },
];

// Use design benefits or default benefits
const displayBenefits = computed(() => {
  const designCards = benefitDesign.value?.cards;
  return designCards && designCards.length > 0 ? designCards : defaultBenefits;
});

// Responsive grid classes based on number of cards
const getGridClasses = () => {
  const cardCount = displayBenefits.value.length;

  if (cardCount === 1) {
    return "grid grid-cols-1 max-w-sm mx-auto gap-6";
  } else if (cardCount === 2) {
    return "grid grid-cols-1 md:grid-cols-2 max-w-2xl mx-auto gap-6";
  } else if (cardCount === 3) {
    return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 max-w-4xl mx-auto gap-6";
  } else if (cardCount === 4) {
    return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 max-w-6xl mx-auto gap-6";
  } else if (cardCount === 5) {
    return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 max-w-7xl mx-auto gap-6";
  } else if (cardCount === 6) {
    return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 max-w-7xl mx-auto gap-6";
  }

  // Default fallback
  return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6";
};
</script>
