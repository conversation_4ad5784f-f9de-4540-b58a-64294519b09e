<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <div 
      v-for="benefit in displayBenefits" 
      :key="benefit.id"
      class="card bg-base-100 shadow-xl"
    >
      <div class="card-body text-center">
        <!-- Icon or Image -->
        <div class="mx-auto mb-4">
          <div v-if="benefit.icon" class="text-4xl">
            {{ benefit.icon }}
          </div>
          <div v-else-if="benefit.image" class="avatar">
            <div class="w-16 rounded-full">
              <NuxtImg :src="benefit.image" :alt="benefit.title" />
            </div>
          </div>
          <div v-else class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary-content" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
        </div>
        
        <!-- Title -->
        <h3 class="card-title justify-center text-lg">{{ benefit.title }}</h3>
        
        <!-- Description -->
        <p class="text-base-content/70">{{ benefit.description }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
const contentStore = useContentStore()

// Get benefit cards from store
const storeBenefits = computed(() => contentStore.activeBenefitCards)

// Default benefits if none are loaded
const defaultBenefits = [
  {
    id: 'default-1',
    title: 'Free Shipping',
    description: 'Free shipping on orders over $50',
    icon: '🚚'
  },
  {
    id: 'default-2',
    title: '24/7 Support',
    description: 'Round-the-clock customer support',
    icon: '💬'
  },
  {
    id: 'default-3',
    title: 'Easy Returns',
    description: '30-day hassle-free returns',
    icon: '↩️'
  },
  {
    id: 'default-4',
    title: 'Secure Payment',
    description: 'Your payment information is safe',
    icon: '🔒'
  }
]

// Use default benefits if no benefits are available
const displayBenefits = computed(() => {
  return storeBenefits.value.length > 0 ? storeBenefits.value : defaultBenefits
})
</script>
