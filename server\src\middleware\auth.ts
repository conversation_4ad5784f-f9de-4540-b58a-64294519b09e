import { PrismaClient, UserRole } from "@prisma/client";
import { Request, Response, NextFunction } from "express";
import { verifyToken, extractTokenFromHeader } from "../utils/auth";

const prisma = new PrismaClient();

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: UserRole;
        firstName: string | null;
        lastName: string | null;
      };
    }
  }
}

/**
 * Middleware to authenticate user
 */
export async function authenticate(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);

    if (!token) {
      res.status(401).json({
        error: "Unauthorized",
        message: "No token provided",
      });
      return;
    }

    const decoded = verifyToken(token);

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true,
      },
    });

    if (!user) {
      res.status(401).json({
        error: "Unauthorized",
        message: "User not found",
      });
      return;
    }

    req.user = user;
    next();
  } catch (error: unknown) {
    res.status(401).json({
      error: "Unauthorized",
      message: "Invalid token",
    });
  }
}

/**
 * Middleware to check if user is admin
 */
export function requireAdmin(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  if (
    !req.user ||
    (req.user.role !== "ADMIN" && req.user.role !== "SUPERADMIN")
  ) {
    res.status(403).json({
      error: "Forbidden",
      message: "Admin access required",
    });
    return;
  }
  next();
}

/**
 * Middleware to check if user is superadmin
 */
export function requireSuperAdmin(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  if (!req.user || req.user.role !== "SUPERADMIN") {
    res.status(403).json({
      error: "Forbidden",
      message: "Super admin access required",
    });
    return;
  }
  next();
}

/**
 * Optional authentication - doesn't fail if no token
 */
export async function optionalAuth(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);

    if (token) {
      const decoded = verifyToken(token);
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          email: true,
          role: true,
          firstName: true,
          lastName: true,
        },
      });

      if (user) {
        req.user = user;
      }
    }

    next();
  } catch (error: unknown) {
    // Continue without user if token is invalid
    next();
  }
}
