import { Request, Response, NextFunction } from "express";

/**
 * Error class for API errors with status codes
 */
export class ApiError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(statusCode: number, message: string, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Create an API error with status code
 */
export const createError = (statusCode: number, message: string): ApiError => {
  return new ApiError(statusCode, message);
};

/**
 * Controller function type - supports generic Request types
 */
type ControllerFunction = (
  req: Request<any, any, any, any>,
  res: Response,
  next: NextFunction
) => Promise<void> | void;

/**
 * Wrapper function to handle async controller errors
 * This ensures all controller errors are properly caught and passed to error middleware
 */
export const wrapController = (fn: ControllerFunction) => {
  return (
    req: Request<any, any, any, any>,
    res: Response,
    next: NextFunction
  ): void => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Send standardized success response
 */
export const sendSuccess = (
  res: Response,
  data: any,
  message?: string,
  statusCode = 200
): void => {
  const response: any = {
    success: true,
    ...(message && { message }),
    ...data,
  };

  res.status(statusCode).json(response);
};

/**
 * Send standardized error response
 */
export const sendError = (
  res: Response,
  error: string,
  message: string,
  statusCode = 500
): void => {
  res.status(statusCode).json({
    success: false,
    error,
    message,
  });
};
