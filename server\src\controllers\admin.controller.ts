import { Request, Response, NextFunction } from "express";
import { PrismaClient, OrderStatus } from "@prisma/client";
import { createError } from "../utils/wrapController";

const prisma = new PrismaClient();

// Request interfaces
export interface AdminProductsQuery {
  page?: string;
  limit?: string;
  search?: string;
}

export interface CreateProductRequest {
  name: string;
  slug: string;
  description?: string;
  shortDescription?: string;
  price: number;
  comparePrice?: number;
  sku?: string;
  barcode?: string;
  trackQuantity: boolean;
  quantity?: number;
  weight?: number;
  isActive: boolean;
  isFeatured: boolean;
  isTrending: boolean;
  isNewArrival: boolean;
  badgeText?: string;
  metaTitle?: string;
  metaDescription?: string;
  categoryId: string;
  images?: Array<{
    url: string;
    altText?: string;
    sortOrder: number;
  }>;
  variants?: Array<{
    name: string;
    value: string;
    price?: number;
    sku?: string;
    quantity?: number;
  }>;
  accordions?: Array<{
    title: string;
    content: string;
    sortOrder: number;
  }>;
}

export interface AdminOrdersQuery {
  page?: string;
  limit?: string;
  status?: string;
}

export interface UpdateOrderStatusRequest {
  status: OrderStatus;
}

/**
 * GET /api/admin/dashboard
 * Get dashboard statistics
 */
export const getDashboard = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const [
      totalProducts,
      totalCategories,
      totalOrders,
      totalUsers,
      recentOrders,
    ] = await Promise.all([
      prisma.product.count({ where: { isActive: true } }),
      prisma.category.count({ where: { isActive: true } }),
      prisma.order.count(),
      prisma.user.count(),
      prisma.order.findMany({
        take: 5,
        orderBy: { createdAt: "desc" },
        include: {
          items: {
            include: {
              product: {
                select: { name: true },
              },
            },
          },
        },
      }),
    ]);

    res.json({
      stats: {
        totalProducts,
        totalCategories,
        totalOrders,
        totalUsers,
      },
      recentOrders,
    });
  } catch (error) {
    console.error("Get dashboard error:", error);
    next(createError(500, "Failed to get dashboard data"));
  }
};

/**
 * GET /api/admin/products
 * Get all products with pagination
 */
export const getAdminProducts = async (
  req: Request<{}, {}, {}, AdminProductsQuery>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const page = parseInt(req.query.page || "1");
    const limit = parseInt(req.query.limit || "20");
    const skip = (page - 1) * limit;
    const search = req.query.search;

    const where: any = {
      ...(search && {
        OR: [
          { name: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
        ],
      }),
    };

    const [products, totalCount] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          category: { select: { name: true, slug: true } },
          images: { orderBy: { sortOrder: "asc" } },
          variants: true,
          _count: { select: { orderItems: true } },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.product.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    res.json({
      products,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    console.error("Get admin products error:", error);
    next(createError(500, "Failed to get products"));
  }
};

/**
 * POST /api/admin/products
 * Create new product
 */
export const createProduct = async (
  req: Request<{}, {}, CreateProductRequest>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const {
      name,
      slug,
      description,
      shortDescription,
      price,
      comparePrice,
      sku,
      barcode,
      trackQuantity,
      quantity,
      weight,
      isActive,
      isFeatured,
      isTrending,
      isNewArrival,
      badgeText,
      metaTitle,
      metaDescription,
      categoryId,
      images,
      variants,
      accordions,
    } = req.body;

    // Check if slug already exists
    const existingProduct = await prisma.product.findUnique({
      where: { slug },
    });

    if (existingProduct) {
      next(createError(409, "Product with this slug already exists"));
      return;
    }

    // Create product with related data
    const product = await prisma.product.create({
      data: {
        name,
        slug,
        description,
        shortDescription,
        price,
        comparePrice,
        sku,
        barcode,
        trackQuantity,
        quantity,
        weight,
        isActive,
        isFeatured,
        isTrending,
        isNewArrival,
        badgeText,
        metaTitle,
        metaDescription,
        categoryId,
        ...(images && {
          images: {
            create: images,
          },
        }),
        ...(variants && {
          variants: {
            create: variants,
          },
        }),
        ...(accordions && {
          accordions: {
            create: accordions,
          },
        }),
      },
      include: {
        category: { select: { name: true, slug: true } },
        images: { orderBy: { sortOrder: "asc" } },
        variants: true,
        accordions: { orderBy: { sortOrder: "asc" } },
      },
    });

    res.status(201).json({
      message: "Product created successfully",
      product,
    });
  } catch (error) {
    console.error("Create product error:", error);
    next(createError(500, "Failed to create product"));
  }
};

/**
 * GET /api/admin/orders
 * Get all orders with pagination
 */
export const getAdminOrders = async (
  req: Request<{}, {}, {}, AdminOrdersQuery>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const page = parseInt(req.query.page || "1");
    const limit = parseInt(req.query.limit || "20");
    const skip = (page - 1) * limit;
    const status = req.query.status;

    const where: any = {
      ...(status && { status }),
    };

    const [orders, totalCount] = await Promise.all([
      prisma.order.findMany({
        where,
        include: {
          items: {
            include: {
              product: {
                select: { name: true, images: { take: 1 } },
              },
            },
          },
          user: {
            select: { firstName: true, lastName: true, email: true },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.order.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    res.json({
      orders,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    console.error("Get admin orders error:", error);
    next(createError(500, "Failed to get orders"));
  }
};

/**
 * PUT /api/admin/orders/:id/status
 * Update order status
 */
export const updateOrderStatus = async (
  req: Request<{ id: string }, {}, UpdateOrderStatusRequest>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // Validate status
    const validStatuses: OrderStatus[] = [
      OrderStatus.PENDING,
      OrderStatus.PROCESSING,
      OrderStatus.SHIPPED,
      OrderStatus.DELIVERED,
      OrderStatus.CANCELLED,
    ];
    if (!validStatuses.includes(status)) {
      next(createError(400, "Invalid order status"));
      return;
    }

    const order = await prisma.order.update({
      where: { id },
      data: { status },
      include: {
        items: {
          include: {
            product: {
              select: { name: true, images: { take: 1 } },
            },
          },
        },
        user: {
          select: { firstName: true, lastName: true, email: true },
        },
      },
    });

    res.json({
      message: "Order status updated successfully",
      order,
    });
  } catch (error) {
    console.error("Update order status error:", error);
    next(createError(500, "Failed to update order status"));
  }
};
