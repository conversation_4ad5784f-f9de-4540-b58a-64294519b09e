import { defineStore } from "pinia";

interface Product {
  id: string;
  name: string;
  slug: string;
  price: string;
  isActive: boolean;
}

interface CartItem {
  id: string;
  product: Product;
  quantity: number;
  selectedVariants: Record<string, any>;
  addedAt: Date;
}

interface CartState {
  items: CartItem[];
  isOpen: boolean;
}

interface CheckoutData {
  items: {
    productId: string;
    quantity: number;
    price: number;
    selectedVariants: Record<string, any>;
  }[];
  totalAmount: number;
  itemCount: number;
}

interface ValidationResult {
  validItems: CartItem[];
  invalidItems: CartItem[];
  isValid: boolean;
}

export const useCartStore = defineStore("cart", {
  state: (): CartState => ({
    items: [],
    isOpen: false,
  }),

  getters: {
    itemCount: (state): number =>
      state.items.reduce((total, item) => total + item.quantity, 0),

    totalPrice: (state): number => {
      return state.items.reduce((total, item) => {
        return total + parseFloat(item.product.price) * item.quantity;
      }, 0);
    },

    formattedTotal: (state): string => {
      const total = state.items.reduce((total, item) => {
        return total + parseFloat(item.product.price) * item.quantity;
      }, 0);
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(total);
    },

    isEmpty: (state): boolean => state.items.length === 0,
  },

  actions: {
    addItem(
      product: Product,
      quantity: number = 1,
      selectedVariants: Record<string, any> = {}
    ): void {
      const existingItemIndex = this.items.findIndex(
        (item) =>
          item.product.id === product.id &&
          JSON.stringify(item.selectedVariants) ===
            JSON.stringify(selectedVariants)
      );

      if (existingItemIndex > -1) {
        // Update quantity of existing item
        this.items[existingItemIndex].quantity += quantity;
      } else {
        // Add new item
        this.items.push({
          id: `${product.id}-${Date.now()}`,
          product,
          quantity,
          selectedVariants,
          addedAt: new Date(),
        });
      }

      // Show success notification
      this.showNotification(`${product.name} added to cart`);
    },

    removeItem(itemId: string): void {
      const itemIndex = this.items.findIndex((item) => item.id === itemId);
      if (itemIndex > -1) {
        const item = this.items[itemIndex];
        this.items.splice(itemIndex, 1);
        this.showNotification(`${item.product.name} removed from cart`);
      }
    },

    updateQuantity(itemId: string, quantity: number): void {
      const item = this.items.find((item) => item.id === itemId);
      if (item) {
        if (quantity <= 0) {
          this.removeItem(itemId);
        } else {
          item.quantity = quantity;
        }
      }
    },

    clearCart(): void {
      this.items = [];
      this.showNotification("Cart cleared");
    },

    openCart(): void {
      this.isOpen = true;
    },

    closeCart(): void {
      this.isOpen = false;
    },

    toggleCart(): void {
      this.isOpen = !this.isOpen;
    },

    showNotification(message: string): void {
      // This will be implemented with a toast notification system
      console.log("Cart notification:", message);
    },

    // Get cart data for checkout
    getCheckoutData(): CheckoutData {
      return {
        items: this.items.map((item) => ({
          productId: item.product.id,
          quantity: item.quantity,
          price: parseFloat(item.product.price),
          selectedVariants: item.selectedVariants,
        })),
        totalAmount: this.totalPrice,
        itemCount: this.itemCount,
      };
    },

    // Validate cart items (check if products are still available)
    async validateCart(): Promise<ValidationResult> {
      const api = useApi();
      const validItems: CartItem[] = [];
      const invalidItems: CartItem[] = [];

      for (const item of this.items) {
        try {
          const response = await api.get<{ product: Product }>(
            `/public/products/${item.product.slug}`
          );
          if (response.product && response.product.isActive) {
            // Update product data with latest info
            item.product = response.product;
            validItems.push(item);
          } else {
            invalidItems.push(item);
          }
        } catch (error: unknown) {
          invalidItems.push(item);
        }
      }

      // Remove invalid items
      if (invalidItems.length > 0) {
        this.items = validItems;
        this.showNotification(
          `${invalidItems.length} item(s) removed from cart (no longer available)`
        );
      }

      return {
        validItems,
        invalidItems,
        isValid: invalidItems.length === 0,
      };
    },
  },

  persist: {
    pick: ["items"],
  },
});
