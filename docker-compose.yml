version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: multistore-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: multistore_prod
      POSTGRES_USER: multistore_user
      POSTGRES_PASSWORD: multistore_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./server/prisma/migrations:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - multistore-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U multistore_user -d multistore_prod"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API Server
  api:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: multistore-api
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DATABASE_URL: "**************************************************************/multistore_prod?schema=public"
      JWT_SECRET: "your-super-secure-jwt-secret-key-for-production"
      JWT_EXPIRES_IN: "7d"
      PORT: 3001
      BASE_URL: "https://yourdomain.com"
      CORS_ORIGIN: "https://yourdomain.com"
    volumes:
      - uploads_data:/app/uploads
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - multistore-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3001/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Client
  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: multistore-client
    restart: unless-stopped
    environment:
      NODE_ENV: production
      NUXT_PUBLIC_API_BASE: "https://yourdomain.com/api"
    ports:
      - "3000:3000"
    depends_on:
      api:
        condition: service_healthy
    networks:
      - multistore-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (Optional - for production)
  nginx:
    image: nginx:alpine
    container_name: multistore-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - uploads_data:/var/www/uploads:ro
    depends_on:
      - client
      - api
    networks:
      - multistore-network

volumes:
  postgres_data:
    driver: local
  uploads_data:
    driver: local

networks:
  multistore-network:
    driver: bridge
