<template>
  <div class="card bg-gradient-to-r from-primary to-secondary text-primary-content shadow-xl">
    <div class="card-body text-center py-16">
      <!-- Title -->
      <h2 class="card-title justify-center text-3xl md:text-4xl font-bold mb-4">
        {{ title }}
      </h2>
      
      <!-- Description -->
      <p class="text-lg md:text-xl mb-8 max-w-2xl mx-auto">
        {{ description }}
      </p>
      
      <!-- Newsletter Form -->
      <form @submit.prevent="handleSubmit" class="max-w-md mx-auto w-full">
        <div class="join w-full">
          <input 
            v-model="email"
            type="email" 
            placeholder="Enter your email address" 
            class="input input-bordered join-item flex-1 text-base-content"
            :class="{ 'input-error': error }"
            required
          />
          <button 
            type="submit" 
            class="btn btn-accent join-item"
            :class="{ 'loading': loading }"
            :disabled="loading"
          >
            <span v-if="!loading">Subscribe</span>
          </button>
        </div>
        
        <!-- Error Message -->
        <div v-if="error" class="alert alert-error mt-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ error }}</span>
        </div>
        
        <!-- Success Message -->
        <div v-if="success" class="alert alert-success mt-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ success }}</span>
        </div>
      </form>
      
      <!-- Privacy Note -->
      <p class="text-sm mt-4 opacity-80">
        We respect your privacy. Unsubscribe at any time.
      </p>
    </div>
  </div>
</template>

<script setup>
const contentStore = useContentStore()

// Form state
const email = ref('')
const loading = ref(false)
const error = ref('')
const success = ref('')

// Get newsletter content from store
const companySettings = computed(() => contentStore.companySettings)

// Newsletter content
const title = computed(() => 
  companySettings.value?.newsletterTitle || 'Subscribe to our newsletter'
)

const description = computed(() => 
  companySettings.value?.newsletterDescription || 'Get the latest updates on new products and exclusive offers.'
)

// Handle form submission
const handleSubmit = async () => {
  if (!email.value) return
  
  loading.value = true
  error.value = ''
  success.value = ''
  
  try {
    await contentStore.subscribeToNewsletter(email.value)
    success.value = 'Thank you for subscribing!'
    email.value = ''
  } catch (err) {
    if (err.status === 409) {
      error.value = 'This email is already subscribed.'
    } else {
      error.value = 'Something went wrong. Please try again.'
    }
  } finally {
    loading.value = false
  }
  
  // Clear messages after 5 seconds
  setTimeout(() => {
    error.value = ''
    success.value = ''
  }, 5000)
}
</script>
