<template>
  <div class="min-h-screen bg-base-100">
    <AppNavbar />

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Breadcrumbs -->
      <div class="breadcrumbs text-sm mb-8">
        <ul>
          <li><NuxtLink to="/">Home</NuxtLink></li>
          <li><NuxtLink to="/products">Products</NuxtLink></li>
          <li>{{ product?.name || slug }}</li>
        </ul>
      </div>

      <div v-if="pending" class="flex justify-center items-center min-h-96">
        <span class="loading loading-spinner loading-lg"></span>
      </div>

      <div v-else-if="error" class="text-center py-16">
        <div class="alert alert-error max-w-md mx-auto">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="stroke-current shrink-0 h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span>Product not found</span>
        </div>
        <NuxtLink to="/" class="btn btn-primary mt-4">
          Return to Home
        </NuxtLink>
      </div>

      <div v-else-if="product" class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- Product Images -->
        <div class="space-y-4">
          <div class="aspect-square bg-base-200 rounded-lg overflow-hidden">
            <NuxtImg
              :src="
                product.images?.[0]?.url ||
                'https://img.daisyui.com/images/stock/photo-1606107557195-0e29a4b5b4aa.webp'
              "
              :alt="product.name"
              class="w-full h-full object-cover"
            />
          </div>

          <!-- Thumbnail images if multiple -->
          <div v-if="product.images?.length > 1" class="grid grid-cols-4 gap-2">
            <div
              v-for="(image, index) in product.images.slice(1, 5)"
              :key="index"
              class="aspect-square bg-base-200 rounded-lg overflow-hidden cursor-pointer hover:opacity-80"
            >
              <NuxtImg
                :src="image.url"
                :alt="`${product.name} ${index + 2}`"
                class="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>

        <!-- Product Details -->
        <div class="space-y-6">
          <div>
            <h1 class="text-3xl font-bold mb-2">{{ product.name }}</h1>
            <div class="flex items-center gap-4 mb-4">
              <div class="text-3xl font-bold text-primary">
                ${{ product.price }}
              </div>
              <div
                v-if="product.comparePrice"
                class="text-xl text-base-content/50 line-through"
              >
                ${{ product.comparePrice }}
              </div>
            </div>

            <!-- Stock Status -->
            <div class="badge badge-success" v-if="product.stock > 0">
              In Stock ({{ product.stock }} available)
            </div>
            <div class="badge badge-error" v-else>Out of Stock</div>
          </div>

          <!-- Description -->
          <div v-if="product.description">
            <h3 class="text-lg font-semibold mb-2">Description</h3>
            <p class="text-base-content/80">{{ product.description }}</p>
          </div>

          <!-- Variants (if any) -->
          <div v-if="product.variants?.length > 0" class="space-y-4">
            <div v-for="variant in product.variants" :key="variant.id">
              <h4 class="font-medium mb-2">{{ variant.name }}</h4>
              <div class="flex flex-wrap gap-2">
                <button
                  v-for="option in variant.options"
                  :key="option.id"
                  class="btn btn-outline btn-sm"
                  :class="{
                    'btn-active':
                      selectedVariants[variant.name] === option.value,
                  }"
                  @click="selectedVariants[variant.name] = option.value"
                >
                  {{ option.value }}
                </button>
              </div>
            </div>
          </div>

          <!-- Quantity Selector -->
          <div class="flex items-center gap-4">
            <span class="font-medium">Quantity:</span>
            <div class="flex items-center gap-2">
              <button
                @click="quantity = Math.max(1, quantity - 1)"
                class="btn btn-circle btn-outline btn-sm"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M20 12H4"
                  />
                </svg>
              </button>
              <span class="w-12 text-center font-medium">{{ quantity }}</span>
              <button
                @click="quantity = Math.min(product.stock || 99, quantity + 1)"
                class="btn btn-circle btn-outline btn-sm"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
              </button>
            </div>
          </div>

          <!-- Add to Cart Button -->
          <div class="flex gap-4">
            <button
              @click="addToCart"
              class="btn btn-primary flex-1"
              :disabled="!product.stock || product.stock === 0"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
              Add to Cart
            </button>
            <button class="btn btn-outline">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                />
              </svg>
            </button>
          </div>

          <!-- Product Info -->
          <div class="border-t pt-6">
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-base-content/70">SKU:</span>
                <span>{{ product.sku || "N/A" }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-base-content/70">Category:</span>
                <span>{{ product.category?.name || "Uncategorized" }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-base-content/70">Tags:</span>
                <span>{{ product.tags?.join(", ") || "None" }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <AppFooter />
    <CartDrawer />
  </div>
</template>

<script setup>
const route = useRoute();
const cartStore = useCartStore();

const slug = route.params.slug;

// Product state
const quantity = ref(1);
const selectedVariants = reactive({});

// Fetch product data
const {
  data: product,
  pending,
  error,
} = await useLazyAsyncData(`product-${slug}`, async () => {
  try {
    const api = useApi();
    const response = await api.get(`/public/products/${slug}`);
    return response.product;
  } catch (err) {
    // Return mock data for development
    if (process.dev) {
      return {
        id: slug,
        name: slug
          .split("-")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" "),
        slug: slug,
        price: "99.99",
        comparePrice: "129.99",
        description:
          "This is a sample product description. In a real application, this would come from your database.",
        stock: 10,
        sku: `SKU-${slug.toUpperCase()}`,
        images: [
          {
            url: "https://img.daisyui.com/images/stock/photo-1606107557195-0e29a4b5b4aa.webp",
          },
        ],
        category: { name: "Electronics" },
        tags: ["popular", "featured"],
      };
    }
    throw err;
  }
});

// Add to cart function
const addToCart = () => {
  if (!product.value) return;

  cartStore.addItem(
    {
      ...product.value,
      selectedVariants: { ...selectedVariants },
    },
    quantity.value
  );

  // Show success message or notification
  console.log(
    "Added to cart:",
    product.value.name,
    "Quantity:",
    quantity.value
  );
};

// Meta tags
useHead({
  title: () =>
    product.value
      ? `${product.value.name} - Multi Store`
      : "Product - Multi Store",
  meta: [
    {
      name: "description",
      content: () => product.value?.description || "Product details",
    },
  ],
});
</script>
