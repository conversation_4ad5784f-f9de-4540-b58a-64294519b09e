<template>
  <div class="space-y-6">
    <!-- Section Header -->
    <div class="border-b pb-4">
      <h3 class="text-xl font-semibold">CTA Button Design</h3>
      <p class="text-base-content/70 mt-1">
        Customize the call-to-action button appearance
      </p>
    </div>

    <!-- Button Design -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h4 class="card-title text-lg">Button Styling</h4>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Button Style -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Button Style</span>
            </label>
            <select
              v-model="localDesign.style"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="default">Default</option>
              <option value="primary">Primary</option>
              <option value="secondary">Secondary</option>
              <option value="accent">Accent</option>
              <option value="neutral">Neutral</option>
              <option value="info">Info</option>
              <option value="success">Success</option>
              <option value="warning">Warning</option>
              <option value="error">Error</option>
              <option value="outline">Outline</option>
              <option value="soft">Soft</option>
              <option value="dash">Dash</option>
              <option value="ghost">Ghost</option>
              <option value="link">Link</option>
              <option value="custom">Custom Colors</option>
            </select>
          </div>

          <!-- Button Size -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Button Size</span>
            </label>
            <select
              v-model="localDesign.size"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="xs">Extra Small</option>
              <option value="sm">Small</option>
              <option value="md">Medium</option>
              <option value="lg">Large</option>
              <option value="xl">Extra Large</option>
            </select>
          </div>

          <!-- Button Rounded Corners -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Rounded Corners</span>
            </label>
            <select
              v-model="localDesign.rounded"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="default">Default</option>
              <option value="none">None (Square)</option>
              <option value="sm">Small</option>
              <option value="md">Medium</option>
              <option value="lg">Large</option>
              <option value="xl">Extra Large</option>
              <option value="2xl">2X Large</option>
              <option value="3xl">3X Large</option>
              <option value="full">Fully Rounded</option>
              <option value="circle">Circle</option>
            </select>
          </div>
        </div>

        <!-- Button Modifiers -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          <!-- Wide Button -->
          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="label-text">Wide (more padding)</span>
              <input
                type="checkbox"
                class="toggle"
                v-model="localDesign.wide"
                @change="updateDesign"
              />
            </label>
          </div>

          <!-- Block Button -->
          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="label-text">Block (full width)</span>
              <input
                type="checkbox"
                class="toggle"
                v-model="localDesign.block"
                @change="updateDesign"
              />
            </label>
          </div>

          <!-- Square Button -->
          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="label-text">Square (1:1 ratio)</span>
              <input
                type="checkbox"
                class="toggle"
                v-model="localDesign.square"
                @change="updateDesign"
              />
            </label>
          </div>

          <!-- Drop Shadow -->
          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="label-text">Drop Shadow</span>
              <input
                type="checkbox"
                class="toggle"
                v-model="localDesign.dropShadow"
                @change="updateDesign"
              />
            </label>
          </div>
        </div>

        <!-- Custom Colors (when not using DaisyUI presets) -->
        <div
          v-if="localDesign.style === 'custom'"
          class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4"
        >
          <!-- Background Color -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Background Color</span>
            </label>
            <input
              v-model="localDesign.backgroundColor"
              type="color"
              class="input input-bordered w-full h-10"
              @input="updateDesign"
            />
          </div>

          <!-- Text Color -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Text Color</span>
            </label>
            <input
              v-model="localDesign.textColor"
              type="color"
              class="input input-bordered w-full h-10"
              @input="updateDesign"
            />
          </div>

          <!-- Border Color -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Border Color</span>
            </label>
            <input
              v-model="localDesign.borderColor"
              type="color"
              class="input input-bordered w-full h-10"
              @input="updateDesign"
            />
          </div>
        </div>

        <!-- Add Custom Style Option -->
        <div class="form-control mt-4">
          <label class="label cursor-pointer">
            <span class="label-text"
              >Use custom colors instead of DaisyUI theme</span
            >
            <input
              type="checkbox"
              class="toggle"
              :checked="localDesign.style === 'custom'"
              @change="toggleCustomStyle"
            />
          </label>
        </div>

        <!-- Preview -->
        <div class="mt-6 p-4 bg-base-200 rounded-lg">
          <div class="text-center">
            <button :class="getButtonClasses()" :style="getButtonStyles()">
              Shop Now
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  design: {
    type: Object,
    required: true,
  },
});

// Emits
const emit = defineEmits(["update:design", "update"]);

// Default design settings based on DaisyUI defaults
const getDefaultDesign = () => ({
  style: "primary",
  size: "lg",
  rounded: "default",
  wide: false,
  block: false,
  square: false,
  dropShadow: false,
  backgroundColor: "#3b82f6",
  textColor: "#ffffff",
  borderColor: "#3b82f6",
});

// Local state
const localDesign = ref({ ...getDefaultDesign(), ...(props.design || {}) });

// Methods
const updateDesign = () => {
  emit("update:design", { ...localDesign.value });
  emit("update");
};

const toggleCustomStyle = (event) => {
  if (event.target.checked) {
    localDesign.value.style = "custom";
  } else {
    localDesign.value.style = "primary";
  }
  updateDesign();
};

const getButtonClasses = () => {
  const classes = ["btn"];

  if (localDesign.value.style !== "custom") {
    // DaisyUI preset styles
    switch (localDesign.value.style) {
      case "primary":
        classes.push("btn-primary");
        break;
      case "secondary":
        classes.push("btn-secondary");
        break;
      case "accent":
        classes.push("btn-accent");
        break;
      case "neutral":
        classes.push("btn-neutral");
        break;
      case "info":
        classes.push("btn-info");
        break;
      case "success":
        classes.push("btn-success");
        break;
      case "warning":
        classes.push("btn-warning");
        break;
      case "error":
        classes.push("btn-error");
        break;
      case "outline":
        classes.push("btn-outline");
        break;
      case "soft":
        classes.push("btn-soft");
        break;
      case "dash":
        classes.push("btn-dash");
        break;
      case "ghost":
        classes.push("btn-ghost");
        break;
      case "link":
        classes.push("btn-link");
        break;
    }
  }

  // Size classes
  switch (localDesign.value.size) {
    case "xs":
      classes.push("btn-xs");
      break;
    case "sm":
      classes.push("btn-sm");
      break;
    case "lg":
      classes.push("btn-lg");
      break;
    case "xl":
      classes.push("btn-xl");
      break;
    // md is default, no class needed
  }

  // Rounded corner classes
  switch (localDesign.value.rounded) {
    case "none":
      classes.push("rounded-none");
      break;
    case "sm":
      classes.push("rounded-sm");
      break;
    case "md":
      classes.push("rounded-md");
      break;
    case "lg":
      classes.push("rounded-lg");
      break;
    case "xl":
      classes.push("rounded-xl");
      break;
    case "2xl":
      classes.push("rounded-2xl");
      break;
    case "3xl":
      classes.push("rounded-3xl");
      break;
    case "full":
      classes.push("rounded-full");
      break;
    case "circle":
      classes.push("btn-circle");
      break;
    // default uses DaisyUI default rounded
  }

  // Modifier classes
  if (localDesign.value.wide) {
    classes.push("btn-wide");
  }

  if (localDesign.value.block) {
    classes.push("btn-block");
  }

  if (localDesign.value.square) {
    classes.push("btn-square");
  }

  if (localDesign.value.dropShadow) {
    classes.push("shadow-lg");
  }

  return classes.join(" ");
};

const getButtonStyles = () => {
  if (localDesign.value.style === "custom") {
    return {
      backgroundColor: localDesign.value.backgroundColor,
      color: localDesign.value.textColor,
      borderColor: localDesign.value.borderColor,
    };
  }
  return {};
};

// Watch for prop changes
watch(
  () => props.design,
  (newDesign) => {
    localDesign.value = { ...getDefaultDesign(), ...newDesign };
  },
  { deep: true }
);
</script>
