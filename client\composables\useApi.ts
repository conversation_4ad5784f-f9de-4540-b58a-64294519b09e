// client/composables/useApi.ts
import type { FetchOptions } from "ofetch";

export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
  success?: boolean;
}

export interface ApiError {
  message: string;
  status: number;
  data?: any;
}

interface ApiOptions {
  showLoading?: boolean;
  showError?: boolean;
  showSuccess?: boolean;
  successMessage?: string;
  loadingMessage?: string;
}

interface ApiMethods {
  get: <T = any>(url: string, config?: FetchOptions & ApiOptions) => Promise<T>;
  post: <T = any>(
    url: string,
    data?: any,
    config?: FetchOptions & ApiOptions
  ) => Promise<T>;
  put: <T = any>(
    url: string,
    data?: any,
    config?: FetchOptions & ApiOptions
  ) => Promise<T>;
  patch: <T = any>(
    url: string,
    data?: any,
    config?: FetchOptions & ApiOptions
  ) => Promise<T>;
  delete: <T = any>(
    url: string,
    config?: FetchOptions & ApiOptions
  ) => Promise<T>;
  upload: <T = any>(
    url: string,
    formData: FormData,
    config?: FetchOptions & ApiOptions
  ) => Promise<T>;
}

export const useApi = (): ApiMethods => {
  const config = useRuntimeConfig();
  const loading = ref(false);
  const authStore = useAuthStore();

  // Helper function to get auth headers
  const getAuthHeaders = () => {
    const headers: Record<string, string> = {};
    if (authStore.token) {
      headers.Authorization = `Bearer ${authStore.token}`;
    }
    return headers;
  };

  const handleRequest = async <T>(
    requestFn: () => Promise<T>,
    options: ApiOptions = {}
  ): Promise<T> => {
    const {
      showLoading = false,
      showError = true,
      showSuccess = false,
      successMessage = "Operation completed successfully",
      loadingMessage = "Loading...",
    } = options;

    try {
      if (showLoading) {
        loading.value = true;
        console.log(loadingMessage);
      }

      const response = await requestFn();

      // Handle different response structures
      let result: T;
      if (response && typeof response === "object" && "data" in response) {
        // Server returns { data: T, message?: string }
        result = (response as ApiResponse<T>).data as T;
      } else {
        // Server returns T directly
        result = response;
      }

      if (showSuccess) {
        console.log(successMessage);
      }

      return result;
    } catch (error: unknown) {
      if (showError) {
        const errorMessage =
          error instanceof Error ? error.message : "An error occurred";
        console.error("API Error:", errorMessage, error);
      }

      throw error;
    } finally {
      if (showLoading) {
        loading.value = false;
      }
    }
  };

  return {
    get: <T = any>(url: string, options: FetchOptions & ApiOptions = {}) => {
      const {
        showLoading,
        showError,
        showSuccess,
        successMessage,
        loadingMessage,
        ...fetchOptions
      } = options;
      const apiOptions = {
        showLoading,
        showError,
        showSuccess,
        successMessage,
        loadingMessage,
      };

      return handleRequest<T>(
        () =>
          $fetch<T>(url, {
            baseURL: config.public.apiBase,
            headers: {
              ...getAuthHeaders(),
              ...fetchOptions.headers,
            },
            ...fetchOptions,
            method: "GET" as const,
          }),
        apiOptions
      );
    },

    post: <T = any>(
      url: string,
      data?: any,
      options: FetchOptions & ApiOptions = {}
    ) => {
      const {
        showLoading,
        showError,
        showSuccess,
        successMessage,
        loadingMessage,
        ...fetchOptions
      } = options;
      const apiOptions = {
        showLoading,
        showError,
        showSuccess,
        successMessage,
        loadingMessage,
      };

      return handleRequest<T>(
        () =>
          $fetch<T>(url, {
            baseURL: config.public.apiBase,
            headers: {
              ...getAuthHeaders(),
              ...fetchOptions.headers,
            },
            body: data,
            ...fetchOptions,
            method: "POST" as const,
          }),
        apiOptions
      );
    },

    put: <T = any>(
      url: string,
      data?: any,
      options: FetchOptions & ApiOptions = {}
    ) => {
      const {
        showLoading,
        showError,
        showSuccess,
        successMessage,
        loadingMessage,
        ...fetchOptions
      } = options;
      const apiOptions = {
        showLoading,
        showError,
        showSuccess,
        successMessage,
        loadingMessage,
      };

      return handleRequest<T>(
        () =>
          $fetch<T>(url, {
            baseURL: config.public.apiBase,
            headers: {
              ...getAuthHeaders(),
              ...fetchOptions.headers,
            },
            body: data,
            ...fetchOptions,
            method: "PUT" as const,
          }),
        apiOptions
      );
    },

    patch: <T = any>(
      url: string,
      data?: any,
      options: FetchOptions & ApiOptions = {}
    ) => {
      const {
        showLoading,
        showError,
        showSuccess,
        successMessage,
        loadingMessage,
        ...fetchOptions
      } = options;
      const apiOptions = {
        showLoading,
        showError,
        showSuccess,
        successMessage,
        loadingMessage,
      };

      return handleRequest<T>(
        () =>
          $fetch<T>(url, {
            baseURL: config.public.apiBase,
            headers: {
              ...getAuthHeaders(),
              ...fetchOptions.headers,
            },
            body: data,
            ...fetchOptions,
            method: "PATCH" as const,
          }),
        apiOptions
      );
    },

    delete: <T = any>(url: string, options: FetchOptions & ApiOptions = {}) => {
      const {
        showLoading,
        showError,
        showSuccess,
        successMessage,
        loadingMessage,
        ...fetchOptions
      } = options;
      const apiOptions = {
        showLoading,
        showError,
        showSuccess,
        successMessage,
        loadingMessage,
      };

      return handleRequest<T>(
        () =>
          $fetch<T>(url, {
            baseURL: config.public.apiBase,
            headers: {
              ...getAuthHeaders(),
              ...fetchOptions.headers,
            },
            ...fetchOptions,
            method: "DELETE" as const,
          }),
        apiOptions
      );
    },

    upload: <T = any>(
      url: string,
      formData: FormData,
      options: FetchOptions & ApiOptions = {}
    ) => {
      const {
        showLoading,
        showError,
        showSuccess,
        successMessage,
        loadingMessage,
        ...fetchOptions
      } = options;
      const apiOptions = {
        showLoading,
        showError,
        showSuccess,
        successMessage,
        loadingMessage,
      };

      return handleRequest<T>(
        () =>
          $fetch<T>(url, {
            baseURL: config.public.apiBase,
            headers: {
              ...getAuthHeaders(),
              ...fetchOptions.headers,
            },
            body: formData,
            ...fetchOptions,
            method: "POST" as const,
          }),
        apiOptions
      );
    },
  };
};
