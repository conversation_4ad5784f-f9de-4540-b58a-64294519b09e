<!-- client/components/AppNavbar.vue -->
<template>
  <div
    :class="[
      'navbar bg-base-100 border-b border-gray-200 sticky top-0 z-50',
      navbarClasses,
    ]"
    :style="navbarStyles"
  >
    <!-- Navbar Content Container with Width Control -->
    <div :class="navbarContentClasses">
      <!-- Mobile menu button -->
      <div class="navbar-start">
        <div class="dropdown lg:hidden">
          <div tabindex="0" role="button" class="btn btn-ghost">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h8m-8 6h16"
              />
            </svg>
          </div>
          <ul
            tabindex="0"
            class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow"
          >
            <li><a href="#hero">Home</a></li>
            <li><a href="#trending">Trending</a></li>
            <li><a href="#categories">Categories</a></li>
            <li><a href="#new-arrivals">New Arrivals</a></li>
          </ul>
        </div>

        <!-- Logo -->
        <NuxtLink to="/" :class="['btn btn-ghost text-xl', logoContainerClass]">
          <div
            v-if="companySettings?.logoUrl"
            :style="logoImageStyle"
            class="mr-2"
          >
            <NuxtImg
              :src="companySettings.logoUrl"
              :alt="companySettings.companyName || 'Store Logo'"
              class="w-full h-full object-contain"
              preset="logo"
            />
          </div>
          <svg
            v-if="!companySettings?.logoUrl"
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
            />
          </svg>
          <span
            v-if="companySettings?.logoText"
            :style="logoTextStyle"
            class="leading-none"
          >
            {{ companySettings.logoText }}
          </span>
          <!-- Show company name only if no logo image AND no logo text -->
          <span v-else-if="!companySettings?.logoUrl">
            {{ companySettings?.companyName || "Multi Store" }}
          </span>
        </NuxtLink>
      </div>

      <!-- Desktop Navigation -->
      <div :class="navigationClasses">
        <ul :class="navigationMenuClasses" :style="navigationStyles">
          <li>
            <a href="#hero" :class="linkClasses" :style="linkStyles">Home</a>
          </li>
          <li>
            <a href="#trending" :class="linkClasses" :style="linkStyles"
              >Trending</a
            >
          </li>
          <li>
            <a href="#categories" :class="linkClasses" :style="linkStyles"
              >Categories</a
            >
          </li>
          <li>
            <a href="#new-arrivals" :class="linkClasses" :style="linkStyles"
              >New Arrivals</a
            >
          </li>
        </ul>
      </div>

      <!-- Right side actions -->
      <div class="navbar-end">
        <!-- Cart with indicator -->
        <div class="dropdown dropdown-end">
          <div
            tabindex="0"
            role="button"
            class="btn btn-ghost btn-circle"
            @click="cartStore.toggleCart()"
          >
            <div class="indicator">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
              <span
                v-if="cartStore.itemCount > 0"
                class="badge badge-sm indicator-item"
                >{{ cartStore.itemCount }}</span
              >
            </div>
          </div>
        </div>

        <!-- User menu -->
        <div v-if="authStore.isAuthenticated" class="dropdown dropdown-end">
          <div
            tabindex="0"
            role="button"
            class="btn btn-ghost btn-circle avatar"
          >
            <div
              class="w-10 rounded-full bg-primary text-primary-content flex items-center justify-center"
            >
              {{ authStore.user?.firstName?.charAt(0) || "U" }}
            </div>
          </div>
          <ul
            tabindex="0"
            class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow"
          >
            <li>
              <a class="justify-between">
                {{ authStore.fullName || authStore.user?.email }}
              </a>
            </li>
            <li><NuxtLink to="/profile">Profile</NuxtLink></li>
            <li><NuxtLink to="/orders">My Orders</NuxtLink></li>
            <li v-if="authStore.isAdmin">
              <NuxtLink to="/admin">Admin Panel</NuxtLink>
            </li>
            <li><button @click="authStore.logout()">Logout</button></li>
          </ul>
        </div>
      </div>
      <!-- Close navbar content container -->
    </div>
  </div>
</template>

<script setup>
const authStore = useAuthStore();
const cartStore = useCartStore();
const contentStore = useContentStore();

// Get company settings for logo and name
const companySettings = computed(() => contentStore.companySettings);

// Get homepage design settings
const homepageDesign = computed(() => contentStore.homepageDesign);
const route = useRoute();

// Only apply custom navbar design on public pages
const navbarDesign = computed(() => {
  if (route.path.startsWith("/admin")) {
    return null; // Use default styling for admin pages
  }
  return homepageDesign.value?.navbarDesign;
});

// Computed style for logo text
const logoTextStyle = computed(() => {
  if (!companySettings.value?.logoTextStyles) return {};

  const styles = companySettings.value.logoTextStyles;
  return {
    fontFamily: styles.fontFamily,
    fontSize: `${styles.fontSize}px`,
    fontWeight: styles.fontWeight,
    fontStyle: styles.fontStyle,
    color: styles.color,
    marginTop: `${styles.marginTop || 0}px`,
    marginRight: `${styles.marginRight || 0}px`,
    marginBottom: `${styles.marginBottom || 0}px`,
    marginLeft: `${styles.marginLeft || 0}px`,
    paddingTop: `${styles.paddingTop || 0}px`,
    paddingRight: `${styles.paddingRight || 0}px`,
    paddingBottom: `${styles.paddingBottom || 0}px`,
    paddingLeft: `${styles.paddingLeft || 0}px`,
  };
});

// Computed alignment classes
const logoContainerClass = computed(() => {
  if (!companySettings.value?.logoTextStyles?.verticalAlign)
    return "flex items-center";

  const align = companySettings.value.logoTextStyles.verticalAlign;
  return [
    "flex",
    align === "top"
      ? "items-start"
      : align === "bottom"
      ? "items-end"
      : "items-center",
  ].join(" ");
});

// Computed style for logo image
const logoImageStyle = computed(() => {
  if (!companySettings.value?.logoImageStyles)
    return { width: "32px", height: "32px" };

  const styles = companySettings.value.logoImageStyles;
  return {
    width: `${styles.width || 32}px`,
    height: styles.maintainAspectRatio
      ? `${styles.width || 32}px`
      : `${styles.height || 32}px`,
    marginTop: `${styles.marginTop || 0}px`,
    marginRight: `${styles.marginRight || 0}px`,
    marginBottom: `${styles.marginBottom || 0}px`,
    marginLeft: `${styles.marginLeft || 0}px`,
    paddingTop: `${styles.paddingTop || 0}px`,
    paddingRight: `${styles.paddingRight || 0}px`,
    paddingBottom: `${styles.paddingBottom || 0}px`,
    paddingLeft: `${styles.paddingLeft || 0}px`,
  };
});

// Navbar styling from design settings
const navbarClasses = computed(() => {
  if (!navbarDesign.value) return "";

  const classes = [];

  // Height classes
  switch (navbarDesign.value.height) {
    case "compact":
      classes.push("h-12");
      break;
    case "large":
      classes.push("h-20");
      break;
    case "xl":
      classes.push("h-24");
      break;
    default:
      classes.push("h-16");
      break;
  }

  return classes.join(" ");
});

const navbarStyles = computed(() => {
  if (!navbarDesign.value) return {};

  const styles = {};

  // Padding
  if (navbarDesign.value.padding) {
    const p = navbarDesign.value.padding;
    styles.paddingTop = `${p.top || 0}px`;
    styles.paddingRight = `${p.right || 0}px`;
    styles.paddingBottom = `${p.bottom || 0}px`;
    styles.paddingLeft = `${p.left || 0}px`;
  }

  // Margin
  if (navbarDesign.value.margin) {
    const m = navbarDesign.value.margin;
    styles.marginTop = `${m.top || 0}px`;
    styles.marginRight = `${m.right || 0}px`;
    styles.marginBottom = `${m.bottom || 0}px`;
    styles.marginLeft = `${m.left || 0}px`;
  }

  return styles;
});

// Navbar content container classes (width control)
const navbarContentClasses = computed(() => {
  if (!navbarDesign.value) return "w-full flex items-center";

  const classes = ["flex items-center"];

  // Width classes - using exact pixel values for consistency
  switch (navbarDesign.value.width) {
    case "1920":
      classes.push("mx-auto px-4");
      classes.push("max-w-[1920px]");
      break;
    case "1600":
      classes.push("mx-auto px-4");
      classes.push("max-w-[1600px]");
      break;
    case "1440":
      classes.push("mx-auto px-4");
      classes.push("max-w-[1440px]");
      break;
    case "1200":
      classes.push("mx-auto px-4");
      classes.push("max-w-[1200px]");
      break;
    case "1024":
      classes.push("mx-auto px-4");
      classes.push("max-w-[1024px]");
      break;
    case "768":
      classes.push("mx-auto px-4");
      classes.push("max-w-[768px]");
      break;
    case "640":
      classes.push("mx-auto px-4");
      classes.push("max-w-[640px]");
      break;
    default:
      classes.push("w-full px-4");
      break;
  }

  return classes.join(" ");
});

// Navigation section classes (alignment control)
const navigationClasses = computed(() => {
  if (!navbarDesign.value) return "navbar-center hidden lg:flex";

  const classes = ["hidden lg:flex"];

  // Alignment classes
  switch (navbarDesign.value.alignment) {
    case "start":
      classes.push("navbar-start");
      break;
    case "end":
      classes.push("navbar-end");
      break;
    default:
      classes.push("navbar-center");
      break;
  }

  return classes.join(" ");
});

// Navigation menu classes (spacing control)
const navigationMenuClasses = computed(() => {
  if (!navbarDesign.value) return "menu menu-horizontal px-1";

  const classes = ["menu menu-horizontal px-1"];

  // Item spacing classes
  switch (navbarDesign.value.itemSpacing) {
    case "tight":
      classes.push("gap-1");
      break;
    case "relaxed":
      classes.push("gap-6");
      break;
    case "loose":
      classes.push("gap-8");
      break;
    default:
      classes.push("gap-4");
      break;
  }

  return classes.join(" ");
});

// Navigation styles
const navigationStyles = computed(() => {
  if (!navbarDesign.value) return {};

  return {};
});

// Link classes
const linkClasses = computed(() => {
  if (!navbarDesign.value) return "";

  const classes = [];

  // Link style classes
  switch (navbarDesign.value.linkStyle) {
    case "ghost":
      classes.push("btn btn-ghost");
      break;
    case "link":
      classes.push("link link-hover");
      break;
    case "outline":
      classes.push("btn btn-outline");
      break;
    case "underline":
      classes.push("link link-hover border-b-2 border-transparent");
      break;
    default:
      classes.push("btn btn-ghost");
      break;
  }

  // Hover style classes
  switch (navbarDesign.value.hoverStyle) {
    case "scale":
      classes.push("hover:scale-105 transition-transform");
      break;
    case "glow":
      classes.push("hover:shadow-lg transition-shadow");
      break;
    case "underline":
      classes.push("hover:border-b-2 hover:border-primary");
      break;
    case "background":
      classes.push("hover:bg-primary hover:text-primary-content");
      break;
    case "none":
      break;
    default:
      classes.push("hover:bg-base-200");
      break;
  }

  return classes.join(" ");
});

// Link styles (text styling)
const linkStyles = computed(() => {
  if (!navbarDesign.value?.textStyles) return {};

  const styles = navbarDesign.value.textStyles;
  return {
    fontFamily: styles.fontFamily,
    fontSize: `${styles.fontSize}px`,
    fontWeight: styles.fontWeight,
    color: styles.color,
  };
});
</script>
