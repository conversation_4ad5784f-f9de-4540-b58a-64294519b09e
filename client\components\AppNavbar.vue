<!-- client/components/AppNavbar.vue -->
<template>
  <!-- 1. Full-width navbar container (always 100% width) -->
  <div
    class="w-full bg-base-100 border-b border-gray-200 sticky top-0 z-50"
    :style="navbarStyles"
  >
    <!-- 2. Content container (width controlled by breakpoints, always centered) -->
    <div :class="navbarContentClasses">
      <!-- Mobile Layout -->
      <div
        class="lg:hidden flex items-center justify-between w-full"
        :style="{ height: navbarHeight }"
      >
        <!-- Mobile menu button + Logo -->
        <div class="flex items-center">
          <div class="dropdown">
            <div tabindex="0" role="button" class="btn btn-ghost mr-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 6h16M4 12h8m-8 6h16"
                />
              </svg>
            </div>
            <ul
              tabindex="0"
              class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow"
            >
              <li><a href="#hero">Home</a></li>
              <li><a href="#trending">Trending</a></li>
              <li><a href="#categories">Categories</a></li>
              <li><a href="#new-arrivals">New Arrivals</a></li>
            </ul>
          </div>

          <!-- Mobile Logo -->
          <NuxtLink
            to="/"
            class="flex items-center text-xl transition-transform duration-200 hover:scale-105"
            :class="logoContainerClass"
          >
            <div
              v-if="companySettings?.logoUrl"
              :style="logoImageStyle"
              class="mr-2"
            >
              <NuxtImg
                :src="companySettings.logoUrl"
                :alt="companySettings.companyName || 'Store Logo'"
                class="w-full h-full object-contain"
                preset="logo"
              />
            </div>
            <svg
              v-if="!companySettings?.logoUrl"
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
              />
            </svg>
            <span
              v-if="companySettings?.logoText"
              :style="logoTextStyle"
              class="leading-none"
            >
              {{ companySettings.logoText }}
            </span>
            <span v-else-if="!companySettings?.logoUrl">
              {{ companySettings?.companyName || "Multi Store" }}
            </span>
          </NuxtLink>
        </div>

        <!-- Mobile Cart -->
        <div class="flex-shrink-0">
          <div class="btn btn-ghost btn-circle" @click="cartStore.toggleCart()">
            <div class="indicator">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"
                />
              </svg>
              <span class="badge badge-sm indicator-item">
                {{ cartStore.totalItems }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Desktop Layout: Logo | Navigation | Cart -->
      <div
        class="hidden lg:flex items-center w-full"
        :style="{ height: navbarHeight }"
      >
        <!-- 3a. Logo (always left) -->
        <div class="flex-shrink-0">
          <NuxtLink
            to="/"
            class="flex items-center text-xl transition-transform duration-200 hover:scale-105"
            :class="logoContainerClass"
          >
            <div
              v-if="companySettings?.logoUrl"
              :style="logoImageStyle"
              class="mr-2"
            >
              <NuxtImg
                :src="companySettings.logoUrl"
                :alt="companySettings.companyName || 'Store Logo'"
                class="w-full h-full object-contain"
                preset="logo"
              />
            </div>
            <svg
              v-if="!companySettings?.logoUrl"
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
              />
            </svg>
            <span
              v-if="companySettings?.logoText"
              :style="logoTextStyle"
              class="leading-none"
            >
              {{ companySettings.logoText }}
            </span>
            <!-- Show company name only if no logo image AND no logo text -->
            <span v-else-if="!companySettings?.logoUrl">
              {{ companySettings?.companyName || "Multi Store" }}
            </span>
          </NuxtLink>
        </div>

        <!-- 3b. Navigation container (grows, pushes logo/cart to corners) -->
        <div class="flex-grow flex" :class="navigationContainerClasses">
          <!-- 4. Navigation links container (content-fit width, alignment controlled) -->
          <div :class="navigationLinksClasses">
            <ul :class="navigationMenuClasses" :style="navigationStyles">
              <li>
                <a href="#hero" :class="linkClasses" :style="linkStyles"
                  >Home</a
                >
              </li>
              <li>
                <a href="#trending" :class="linkClasses" :style="linkStyles"
                  >Trending</a
                >
              </li>
              <li>
                <a href="#categories" :class="linkClasses" :style="linkStyles"
                  >Categories</a
                >
              </li>
              <li>
                <a href="#new-arrivals" :class="linkClasses" :style="linkStyles"
                  >New Arrivals</a
                >
              </li>
            </ul>
          </div>
        </div>

        <!-- 3c. Cart icon (always right) -->
        <div class="flex-shrink-0">
          <!-- Cart with indicator -->
          <div class="dropdown dropdown-end">
            <div
              tabindex="0"
              role="button"
              class="btn btn-ghost btn-circle"
              @click="cartStore.toggleCart()"
            >
              <div class="indicator">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
                  />
                </svg>
                <span
                  v-if="cartStore.itemCount > 0"
                  class="badge badge-sm indicator-item"
                  >{{ cartStore.itemCount }}</span
                >
              </div>
            </div>
          </div>

          <!-- User menu -->
          <div v-if="authStore.isAuthenticated" class="dropdown dropdown-end">
            <div tabindex="0" role="button" class="btn btn-ghost btn-circle">
              <div class="indicator">
                <!-- User icon -->
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
                <!-- Green dot indicator -->
                <span
                  class="indicator-item w-2 h-2 bg-green-400 border border-green-400 rounded-full indicator-bottom indicator-end"
                ></span>
              </div>
            </div>
            <ul
              tabindex="0"
              class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow"
            >
              <li>
                <a class="justify-between">
                  {{ authStore.fullName || authStore.user?.email }}
                </a>
              </li>
              <li><NuxtLink to="/profile">Profile</NuxtLink></li>
              <li><NuxtLink to="/orders">My Orders</NuxtLink></li>
              <li v-if="authStore.isAdmin">
                <NuxtLink to="/admin">Admin Panel</NuxtLink>
              </li>
              <li><button @click="authStore.logout()">Logout</button></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const authStore = useAuthStore();
const cartStore = useCartStore();
const contentStore = useContentStore();

// Get company settings for logo and name
const companySettings = computed(() => contentStore.companySettings);

// Get homepage design settings
const homepageDesign = computed(() => contentStore.homepageDesign);
const route = useRoute();

// Only apply custom navbar design on public pages
const navbarDesign = computed(() => {
  if (route.path.startsWith("/admin")) {
    return null; // Use default styling for admin pages
  }
  return homepageDesign.value?.navbarDesign;
});

// Computed style for logo text
const logoTextStyle = computed(() => {
  if (!companySettings.value?.logoTextStyles) return {};

  const styles = companySettings.value.logoTextStyles;
  return {
    fontFamily: styles.fontFamily,
    fontSize: `${styles.fontSize}px`,
    fontWeight: styles.fontWeight,
    fontStyle: styles.fontStyle,
    color: styles.color,
    marginTop: `${styles.marginTop || 0}px`,
    marginRight: `${styles.marginRight || 0}px`,
    marginBottom: `${styles.marginBottom || 0}px`,
    marginLeft: `${styles.marginLeft || 0}px`,
    paddingTop: `${styles.paddingTop || 0}px`,
    paddingRight: `${styles.paddingRight || 0}px`,
    paddingBottom: `${styles.paddingBottom || 0}px`,
    paddingLeft: `${styles.paddingLeft || 0}px`,
  };
});

// Computed alignment classes
const logoContainerClass = computed(() => {
  if (!companySettings.value?.logoTextStyles?.verticalAlign)
    return "flex items-center";

  const align = companySettings.value.logoTextStyles.verticalAlign;
  return [
    "flex",
    align === "top"
      ? "items-start"
      : align === "bottom"
      ? "items-end"
      : "items-center",
  ].join(" ");
});

// Computed style for logo image
const logoImageStyle = computed(() => {
  if (!companySettings.value?.logoImageStyles)
    return { width: "32px", height: "32px" };

  const styles = companySettings.value.logoImageStyles;
  return {
    width: `${styles.width || 32}px`,
    height: styles.maintainAspectRatio
      ? `${styles.width || 32}px`
      : `${styles.height || 32}px`,
    marginTop: `${styles.marginTop || 0}px`,
    marginRight: `${styles.marginRight || 0}px`,
    marginBottom: `${styles.marginBottom || 0}px`,
    marginLeft: `${styles.marginLeft || 0}px`,
    paddingTop: `${styles.paddingTop || 0}px`,
    paddingRight: `${styles.paddingRight || 0}px`,
    paddingBottom: `${styles.paddingBottom || 0}px`,
    paddingLeft: `${styles.paddingLeft || 0}px`,
  };
});

// Navbar content container classes (width control)
const navbarContentClasses = computed(() => {
  if (!navbarDesign.value) return "flex items-center w-full px-4";

  const classes = ["flex items-center px-4"];

  // Width classes - using exact pixel values for consistency
  switch (navbarDesign.value.width) {
    case "1920":
      classes.push("mx-auto");
      classes.push("max-w-[1920px]");
      break;
    case "1600":
      classes.push("mx-auto");
      classes.push("max-w-[1600px]");
      break;
    case "1440":
      classes.push("mx-auto");
      classes.push("max-w-[1440px]");
      break;
    case "1200":
      classes.push("mx-auto");
      classes.push("max-w-[1200px]");
      break;
    case "1024":
      classes.push("mx-auto");
      classes.push("max-w-[1024px]");
      break;
    case "768":
      classes.push("mx-auto");
      classes.push("max-w-[768px]");
      break;
    case "640":
      classes.push("mx-auto");
      classes.push("max-w-[640px]");
      break;
    default:
      classes.push("w-full");
      break;
  }

  return classes.join(" ");
});

// Navbar height as CSS property
const navbarHeight = computed(() => {
  if (!navbarDesign.value) return "4rem"; // 64px default

  switch (navbarDesign.value.height) {
    case "compact":
      return "3rem"; // 48px
    case "large":
      return "5rem"; // 80px
    case "xl":
      return "6rem"; // 96px
    default:
      return "4rem"; // 64px
  }
});

// Navigation container classes (alignment control for the growing container)
const navigationContainerClasses = computed(() => {
  if (!navbarDesign.value) return "justify-center";

  const classes = [];

  // Alignment classes for the growing container
  switch (navbarDesign.value.alignment) {
    case "start":
      classes.push("justify-start");
      break;
    case "end":
      classes.push("justify-end");
      break;
    default:
      classes.push("justify-center");
      break;
  }

  return classes.join(" ");
});

// Navigation links container classes (content-fit width)
const navigationLinksClasses = computed(() => {
  return "flex items-center"; // Always content-fit width
});

const navbarStyles = computed(() => {
  if (!navbarDesign.value) return {};

  const styles = {};

  // Padding
  if (navbarDesign.value.padding) {
    const p = navbarDesign.value.padding;
    styles.paddingTop = `${p.top || 0}px`;
    styles.paddingRight = `${p.right || 0}px`;
    styles.paddingBottom = `${p.bottom || 0}px`;
    styles.paddingLeft = `${p.left || 0}px`;
  }

  // Margin
  if (navbarDesign.value.margin) {
    const m = navbarDesign.value.margin;
    styles.marginTop = `${m.top || 0}px`;
    styles.marginRight = `${m.right || 0}px`;
    styles.marginBottom = `${m.bottom || 0}px`;
    styles.marginLeft = `${m.left || 0}px`;
  }

  return styles;
});

// Navigation menu classes (spacing control)
const navigationMenuClasses = computed(() => {
  if (!navbarDesign.value) return "flex items-center";

  const classes = ["flex items-center"];

  // Item spacing classes
  switch (navbarDesign.value.itemSpacing) {
    case "tight":
      classes.push("gap-2");
      break;
    case "relaxed":
      classes.push("gap-8");
      break;
    case "loose":
      classes.push("gap-12");
      break;
    default:
      classes.push("gap-6");
      break;
  }

  return classes.join(" ");
});

// Navigation styles
const navigationStyles = computed(() => {
  if (!navbarDesign.value) return {};

  return {};
});

// Link classes
const linkClasses = computed(() => {
  if (!navbarDesign.value) return "";

  const classes = [];

  // Link style classes
  switch (navbarDesign.value.linkStyle) {
    case "ghost":
      classes.push("btn btn-ghost");
      break;
    case "link":
      classes.push("link link-hover");
      break;
    case "outline":
      classes.push("btn btn-outline");
      break;
    case "underline":
      classes.push("link link-hover border-b-2 border-transparent");
      break;
    default:
      classes.push("btn btn-ghost");
      break;
  }

  // Hover style classes
  switch (navbarDesign.value.hoverStyle) {
    case "scale":
      classes.push("hover:scale-105 transition-transform");
      break;
    case "glow":
      classes.push("hover:shadow-lg transition-shadow");
      break;
    case "underline":
      classes.push("hover:border-b-2 hover:border-primary");
      break;
    case "background":
      classes.push("hover:bg-primary hover:text-primary-content");
      break;
    case "none":
      break;
    default:
      classes.push("hover:bg-base-200");
      break;
  }

  return classes.join(" ");
});

// Link styles (text styling)
const linkStyles = computed(() => {
  if (!navbarDesign.value?.textStyles) return {};

  const styles = navbarDesign.value.textStyles;
  return {
    fontFamily: styles.fontFamily,
    fontSize: `${styles.fontSize}px`,
    fontWeight: styles.fontWeight,
    color: styles.color,
  };
});
</script>
