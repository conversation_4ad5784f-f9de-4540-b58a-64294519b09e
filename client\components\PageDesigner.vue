<template>
  <div class="space-y-8">
    <!-- Section Header -->
    <div class="border-b pb-4">
      <h2 class="text-2xl font-semibold">Page Background Design</h2>
      <p class="text-base-content/70 mt-1">
        Customize the overall page background and appearance
      </p>
    </div>

    <!-- Background Type -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h3 class="card-title text-lg">Background Type</h3>

        <div class="form-control mb-4">
          <div class="flex gap-4">
            <label class="label cursor-pointer">
              <input
                v-model="localDesign.backgroundType"
                type="radio"
                value="color"
                class="radio radio-primary"
                @change="updateDesign"
              />
              <span class="label-text ml-2">Solid Color</span>
            </label>
            <label class="label cursor-pointer">
              <input
                v-model="localDesign.backgroundType"
                type="radio"
                value="gradient"
                class="radio radio-primary"
                @change="updateDesign"
              />
              <span class="label-text ml-2">Gradient</span>
            </label>
            <label class="label cursor-pointer">
              <input
                v-model="localDesign.backgroundType"
                type="radio"
                value="image"
                class="radio radio-primary"
                @change="updateDesign"
              />
              <span class="label-text ml-2">Background Image</span>
            </label>
          </div>
        </div>

        <!-- Solid Color -->
        <div v-if="localDesign.backgroundType === 'color'" class="form-control">
          <label class="label">
            <span class="label-text font-medium">Background Color</span>
          </label>
          <input
            v-model="localDesign.backgroundColor"
            type="color"
            class="input input-bordered w-20 h-12"
            @input="updateDesign"
          />
        </div>

        <!-- Gradient -->
        <div
          v-else-if="localDesign.backgroundType === 'gradient'"
          class="space-y-4"
        >
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">From Color</span>
              </label>
              <input
                v-model="localDesign.gradient.from"
                type="color"
                class="input input-bordered w-20 h-12"
                @input="updateDesign"
              />
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">To Color</span>
              </label>
              <input
                v-model="localDesign.gradient.to"
                type="color"
                class="input input-bordered w-20 h-12"
                @input="updateDesign"
              />
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Direction</span>
              </label>
              <select
                v-model="localDesign.gradient.direction"
                class="select select-bordered"
                @change="updateDesign"
              >
                <option value="to-r">Left to Right</option>
                <option value="to-l">Right to Left</option>
                <option value="to-b">Top to Bottom</option>
                <option value="to-t">Bottom to Top</option>
                <option value="to-br">Top-Left to Bottom-Right</option>
                <option value="to-bl">Top-Right to Bottom-Left</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Background Image -->
        <div
          v-else-if="localDesign.backgroundType === 'image'"
          class="space-y-4"
        >
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium"
                >Upload Background Image</span
              >
            </label>
            <input
              type="file"
              accept="image/*"
              class="file-input file-input-bordered"
              @change="handleImageUpload"
            />
          </div>

          <div v-if="localDesign.backgroundImage" class="form-control">
            <label class="label">
              <span class="label-text font-medium">Preview</span>
            </label>
            <img
              :src="localDesign.backgroundImage"
              alt="Background"
              class="w-32 h-32 object-cover rounded"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Overlay Settings -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h3 class="card-title text-lg">Overlay Settings</h3>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Overlay Type -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Overlay Type</span>
            </label>
            <select
              v-model="localDesign.overlay.type"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="none">No Overlay</option>
              <option value="color">Color Overlay</option>
              <option value="grayscale">Grayscale</option>
            </select>
          </div>

          <!-- Overlay Color -->
          <div v-if="localDesign.overlay.type === 'color'" class="form-control">
            <label class="label">
              <span class="label-text font-medium">Overlay Color</span>
            </label>
            <input
              v-model="localDesign.overlay.color"
              type="color"
              class="input input-bordered w-20 h-12"
              @input="updateDesign"
            />
          </div>

          <!-- Overlay Opacity -->
          <div v-if="localDesign.overlay.type !== 'none'" class="form-control">
            <label class="label">
              <span class="label-text font-medium">Opacity</span>
            </label>
            <input
              v-model.number="localDesign.overlay.opacity"
              type="range"
              min="0"
              max="1"
              step="0.1"
              class="range range-primary"
              @input="updateDesign"
            />
            <div class="text-sm text-center mt-1">
              {{ Math.round(localDesign.overlay.opacity * 100) }}%
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Preview -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h3 class="card-title text-lg">Preview</h3>
        <div
          class="w-full h-64 rounded-lg relative overflow-hidden"
          :style="getPreviewStyle()"
        >
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="text-center">
              <h2 class="text-2xl font-bold mb-2">Sample Page Content</h2>
              <p class="text-base-content/70">
                This shows how your page background will look
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  design: {
    type: Object,
    required: true,
  },
});

// Emits
const emit = defineEmits(["update:design", "update"]);

// Local state
const localDesign = ref({
  backgroundColor: "#ffffff",
  backgroundType: "color",
  gradient: { from: "#ffffff", to: "#f3f4f6", direction: "to-b" },
  backgroundImage: null,
  overlay: { type: "none", color: "#000000", opacity: 0.5 },
  ...(props.design || {}),
});

// Methods
const updateDesign = () => {
  emit("update:design", { ...localDesign.value });
  emit("update");
};

const handleImageUpload = async (event) => {
  const file = event.target.files[0];
  if (file) {
    // In a real implementation, you would upload to your server
    const imageUrl = URL.createObjectURL(file);
    localDesign.value.backgroundImage = imageUrl;
    updateDesign();
  }
};

const getPreviewStyle = () => {
  const design = localDesign.value;
  let style = {};

  if (design.backgroundType === "color") {
    style.backgroundColor = design.backgroundColor;
  } else if (design.backgroundType === "gradient") {
    const direction = design.gradient.direction.replace("to-", "");
    style.background = `linear-gradient(${direction}, ${design.gradient.from}, ${design.gradient.to})`;
  } else if (design.backgroundType === "image" && design.backgroundImage) {
    style.backgroundImage = `url(${design.backgroundImage})`;
    style.backgroundSize = "cover";
    style.backgroundPosition = "center";
  }

  // Apply overlay
  if (design.overlay.type === "color") {
    style.position = "relative";
    style["&::before"] = {
      content: '""',
      position: "absolute",
      inset: 0,
      backgroundColor: design.overlay.color,
      opacity: design.overlay.opacity,
    };
  } else if (design.overlay.type === "grayscale") {
    style.filter = `grayscale(${design.overlay.opacity * 100}%)`;
  }

  return style;
};

// Watch for prop changes
watch(
  () => props.design,
  (newDesign) => {
    localDesign.value = { ...newDesign };
  },
  { deep: true }
);
</script>
