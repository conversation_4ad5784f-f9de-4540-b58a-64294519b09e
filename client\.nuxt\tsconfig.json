{"compilerOptions": {"paths": {"nitropack/types": ["../node_modules/.pnpm/nitropack@2.11.13/node_modules/nitropack/types"], "nitropack/runtime": ["../node_modules/.pnpm/nitropack@2.11.13/node_modules/nitropack/runtime"], "nitropack": ["../node_modules/.pnpm/nitropack@2.11.13/node_modules/nitropack"], "defu": ["../node_modules/.pnpm/defu@6.1.4/node_modules/defu"], "h3": ["../node_modules/.pnpm/h3@1.15.3/node_modules/h3"], "consola": ["../node_modules/.pnpm/consola@3.4.2/node_modules/consola"], "ofetch": ["../node_modules/.pnpm/ofetch@1.4.1/node_modules/ofetch"], "@unhead/vue": ["../node_modules/.pnpm/@unhead+vue@2.0.12_vue@3.5.17_typescript@5.8.3_/node_modules/@unhead/vue"], "@nuxt/devtools": ["../node_modules/.pnpm/@nuxt+devtools@2.6.2_vite@6.3.5_@types+node@24.0.13_jiti@2.4.2_terser@5.43.1_yaml@2.8.0__vue@3.5.17_typescript@5.8.3_/node_modules/@nuxt/devtools"], "@vue/runtime-core": ["../node_modules/.pnpm/@vue+runtime-core@3.5.17/node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/.pnpm/@vue+compiler-sfc@3.5.17/node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../node_modules/.pnpm/unplugin-vue-router@0.14.0_@vue+compiler-sfc@3.5.17_vue-router@4.5.1_vue@3.5.17_typescript@5._hulvjsuxwqhkdwpclvfik5ji2a/node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/.pnpm/@nuxt+schema@3.17.7/node_modules/@nuxt/schema"], "nuxt": ["../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt"], "vite/client": ["../node_modules/.pnpm/vite@6.3.5_@types+node@24.0.13_jiti@2.4.2_terser@5.43.1_yaml@2.8.0/node_modules/vite/client"], "~": [".."], "~/*": ["../*"], "@": [".."], "@/*": ["../*"], "~~": [".."], "~~/*": ["../*"], "@@": [".."], "@@/*": ["../*"], "#shared": ["../shared"], "assets": ["../assets"], "assets/*": ["../assets/*"], "public": ["../public"], "public/*": ["../public/*"], "#app": ["../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app"], "#app/*": ["../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/*"], "vue-demi": ["../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/compat/vue-demi"], "#image": ["../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3.2_ioredis@5.6.1_magicast@0.3.5/node_modules/@nuxt/image/dist/runtime"], "#image/*": ["../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3.2_ioredis@5.6.1_magicast@0.3.5/node_modules/@nuxt/image/dist/runtime/*"], "#vue-router": ["../node_modules/.pnpm/vue-router@4.5.1_vue@3.5.17_typescript@5.8.3_/node_modules/vue-router"], "#unhead/composables": ["../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/composables/v3"], "#imports": ["./imports"], "#app-manifest": ["./manifest/meta/dev"], "#components": ["./components"], "#build": ["."], "#build/*": ["./*"]}, "esModuleInterop": true, "skipLibCheck": true, "target": "ESNext", "allowJs": true, "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "verbatimModuleSyntax": true, "strict": true, "noUncheckedIndexedAccess": false, "forceConsistentCasingInFileNames": true, "noImplicitOverride": true, "module": "ESNext", "noEmit": true, "lib": ["ESNext", "dom", "dom.iterable", "webworker"], "jsx": "preserve", "jsxImportSource": "vue", "types": [], "moduleResolution": "<PERSON><PERSON><PERSON>", "useDefineForClassFields": true, "noImplicitThis": true, "allowSyntheticDefaultImports": true}, "include": ["../**/*", "../.config/nuxt.*", "./nuxt.d.ts", "../node_modules/.pnpm/@nuxtjs+tailwindcss@6.14.0_magicast@0.3.5/node_modules/@nuxtjs/tailwindcss/runtime", "../node_modules/.pnpm/@nuxtjs+tailwindcss@6.14.0_magicast@0.3.5/node_modules/@nuxtjs/tailwindcss/dist/runtime", "../node_modules/.pnpm/@pinia+nuxt@0.11.1_magicast@0.3.5_pinia@3.0.3_typescript@5.8.3_vue@3.5.17_typescript@5.8.3__/node_modules/@pinia/nuxt/runtime", "../node_modules/.pnpm/@pinia+nuxt@0.11.1_magicast@0.3.5_pinia@3.0.3_typescript@5.8.3_vue@3.5.17_typescript@5.8.3__/node_modules/@pinia/nuxt/dist/runtime", "../../../../../runtime", "../../../../../dist/runtime", "../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3.2_ioredis@5.6.1_magicast@0.3.5/node_modules/@nuxt/image/runtime", "../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3.2_ioredis@5.6.1_magicast@0.3.5/node_modules/@nuxt/image/dist/runtime", "../node_modules/.pnpm/@nuxt+icon@1.15.0_magicast@0.3.5_vite@6.3.5_@types+node@24.0.13_jiti@2.4.2_terser@5.43.1_yaml_wewk6rl4tdmtvypb2ndrpw4rxe/node_modules/@nuxt/icon/runtime", "../node_modules/.pnpm/@nuxt+icon@1.15.0_magicast@0.3.5_vite@6.3.5_@types+node@24.0.13_jiti@2.4.2_terser@5.43.1_yaml_wewk6rl4tdmtvypb2ndrpw4rxe/node_modules/@nuxt/icon/dist/runtime", "../../../../../../../AppData/Roaming/npm/node_modules/@nuxt/devtools/runtime", "../../../../../../../AppData/Roaming/npm/node_modules/@nuxt/devtools/dist/runtime", "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/runtime", "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/dist/runtime", ".."], "exclude": ["../dist", "../.data", "../node_modules", "../../node_modules", "../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/node_modules", "../node_modules/.pnpm/@nuxtjs+tailwindcss@6.14.0_magicast@0.3.5/node_modules/@nuxtjs/tailwindcss/node_modules", "../node_modules/.pnpm/@pinia+nuxt@0.11.1_magicast@0.3.5_pinia@3.0.3_typescript@5.8.3_vue@3.5.17_typescript@5.8.3__/node_modules/@pinia/nuxt/node_modules", "../node_modules/.pnpm/pinia-plugin-persistedstate@4.4.1_@nuxt+kit@3.17.7_magicast@0.3.5__@pinia+nuxt@0.11.1_magicas_eyi6v366vwczah6f7v6tybvmga/node_modules/pinia-plugin-persistedstate/node_modules", "../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3.2_ioredis@5.6.1_magicast@0.3.5/node_modules/@nuxt/image/node_modules", "../node_modules/.pnpm/@nuxt+icon@1.15.0_magicast@0.3.5_vite@6.3.5_@types+node@24.0.13_jiti@2.4.2_terser@5.43.1_yaml_wewk6rl4tdmtvypb2ndrpw4rxe/node_modules/@nuxt/icon/node_modules", "../../../../../../../AppData/Roaming/npm/node_modules/@nuxt/devtools/node_modules", "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/node_modules", "../node_modules/.pnpm/@nuxtjs+tailwindcss@6.14.0_magicast@0.3.5/node_modules/@nuxtjs/tailwindcss/runtime/server", "../node_modules/.pnpm/@nuxtjs+tailwindcss@6.14.0_magicast@0.3.5/node_modules/@nuxtjs/tailwindcss/dist/runtime/server", "../node_modules/.pnpm/@pinia+nuxt@0.11.1_magicast@0.3.5_pinia@3.0.3_typescript@5.8.3_vue@3.5.17_typescript@5.8.3__/node_modules/@pinia/nuxt/runtime/server", "../node_modules/.pnpm/@pinia+nuxt@0.11.1_magicast@0.3.5_pinia@3.0.3_typescript@5.8.3_vue@3.5.17_typescript@5.8.3__/node_modules/@pinia/nuxt/dist/runtime/server", "../../../../../runtime/server", "../../../../../dist/runtime/server", "../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3.2_ioredis@5.6.1_magicast@0.3.5/node_modules/@nuxt/image/runtime/server", "../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3.2_ioredis@5.6.1_magicast@0.3.5/node_modules/@nuxt/image/dist/runtime/server", "../node_modules/.pnpm/@nuxt+icon@1.15.0_magicast@0.3.5_vite@6.3.5_@types+node@24.0.13_jiti@2.4.2_terser@5.43.1_yaml_wewk6rl4tdmtvypb2ndrpw4rxe/node_modules/@nuxt/icon/runtime/server", "../node_modules/.pnpm/@nuxt+icon@1.15.0_magicast@0.3.5_vite@6.3.5_@types+node@24.0.13_jiti@2.4.2_terser@5.43.1_yaml_wewk6rl4tdmtvypb2ndrpw4rxe/node_modules/@nuxt/icon/dist/runtime/server", "../../../../../../../AppData/Roaming/npm/node_modules/@nuxt/devtools/runtime/server", "../../../../../../../AppData/Roaming/npm/node_modules/@nuxt/devtools/dist/runtime/server", "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/runtime/server", "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/dist/runtime/server", "dev"]}