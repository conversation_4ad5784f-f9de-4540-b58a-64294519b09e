# Multi Store - E-commerce Platform

A full-stack e-commerce platform built with Nuxt.js, Express.js, Prisma, and PostgreSQL.

## Features

- 🛍️ Multi-product store with categories
- 🔐 Admin authentication and dashboard
- 🖼️ Logo and company branding management
- 📱 Responsive design with DaisyUI
- 🐳 Docker containerization
- 📁 File upload system for logos and images
- 🛒 Shopping cart functionality
- 📧 Newsletter subscription

## Tech Stack

- **Frontend**: Nuxt.js 3, Vue 3, Tai<PERSON>wind CSS, DaisyUI
- **Backend**: Express.js, Prisma ORM
- **Database**: PostgreSQL
- **Authentication**: JWT
- **File Storage**: Local filesystem with Docker volumes
- **Deployment**: Docker & Docker Compose

## Quick Start

### Development (Local)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd multi-store
   ```

2. **Install dependencies**
   ```bash
   # Backend
   cd server
   pnpm install
   
   # Frontend
   cd ../client
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   # Copy environment files
   cp server/.env.example server/.env
   cp client/.env.local.example client/.env.local
   
   # Edit the files with your configuration
   ```

4. **Start PostgreSQL** (using Docker)
   ```bash
   docker run --name postgres-dev -e POSTGRES_DB=multistore_dev -e POSTGRES_USER=multistore_user -e POSTGRES_PASSWORD=multistore_password -p 5432:5432 -d postgres:15-alpine
   ```

5. **Run database migrations**
   ```bash
   cd server
   pnpm prisma migrate dev
   pnpm prisma db seed
   ```

6. **Start development servers**
   ```bash
   # Backend (Terminal 1)
   cd server
   pnpm run dev
   
   # Frontend (Terminal 2)
   cd client
   pnpm run dev
   ```

7. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001
   - Admin Login: http://localhost:3000/login

### Production (Docker)

1. **Clone and configure**
   ```bash
   git clone <repository-url>
   cd multi-store
   ```

2. **Update production environment**
   ```bash
   # Edit production environment files
   nano server/.env.production
   nano client/.env.production
   nano docker-compose.yml
   
   # Update domain names and secrets
   ```

3. **Build and deploy**
   ```bash
   # Build and start all services
   docker-compose up -d
   
   # Run database migrations
   docker-compose exec api pnpm prisma migrate deploy
   docker-compose exec api pnpm prisma db seed
   ```

4. **Access the application**
   - Frontend: https://yourdomain.com
   - Admin: https://yourdomain.com/login

## File Upload System

The application includes a complete file upload system for logos and images:

### How it works:
1. **Upload**: Files are uploaded to `/server/uploads/` directory
2. **Storage**: Files are stored with unique names to prevent conflicts
3. **Database**: Only relative paths are stored in the database
4. **Serving**: Files are served via static route `/uploads/*`
5. **Docker**: Uploads directory is mapped to a Docker volume for persistence

### Supported formats:
- Images: JPG, JPEG, PNG, GIF, SVG, WebP
- Max size: 2MB per file

### API Endpoints:
- `POST /api/admin/content/upload-logo` - Upload company logo
- `DELETE /api/admin/content/remove-logo` - Remove company logo
- `GET /uploads/logos/filename.jpg` - Serve uploaded files

## Admin Features

### Company Settings (`/admin/settings`)
- Company information (name, contact details)
- Logo upload and management
- Social media links
- SEO settings
- Newsletter configuration

### Default Admin Credentials
- Email: `<EMAIL>`
- Password: `Onamission#007`
- Role: SUPERADMIN

## Docker Volumes

The application uses Docker volumes for data persistence:

- `postgres_data` - Database data
- `uploads_data` - Uploaded files (logos, images)

### Volume Management
```bash
# List volumes
docker volume ls

# Backup uploads
docker run --rm -v uploads_data:/data -v $(pwd):/backup alpine tar czf /backup/uploads-backup.tar.gz -C /data .

# Restore uploads
docker run --rm -v uploads_data:/data -v $(pwd):/backup alpine tar xzf /backup/uploads-backup.tar.gz -C /data
```

## Environment Variables

### Server (.env.production)
```env
NODE_ENV=production
DATABASE_URL=************************************/db
JWT_SECRET=your-secret-key
BASE_URL=https://yourdomain.com
CORS_ORIGIN=https://yourdomain.com
```

### Client (.env.production)
```env
NODE_ENV=production
NUXT_PUBLIC_API_BASE=https://yourdomain.com/api
```

## SSL/HTTPS Setup

For production, place your SSL certificates in the `ssl/` directory:
- `ssl/fullchain.pem` - Full certificate chain
- `ssl/privkey.pem` - Private key

## Monitoring & Logs

```bash
# View logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f api
docker-compose logs -f client

# Monitor resource usage
docker stats
```

## Backup & Restore

### Database Backup
```bash
docker-compose exec postgres pg_dump -U multistore_user multistore_prod > backup.sql
```

### Database Restore
```bash
docker-compose exec -T postgres psql -U multistore_user multistore_prod < backup.sql
```

## Troubleshooting

### Common Issues

1. **Port conflicts**: Make sure ports 3000, 3001, and 5432 are available
2. **Permission issues**: Ensure Docker has permission to create volumes
3. **Environment variables**: Check that all required env vars are set
4. **Database connection**: Verify PostgreSQL is running and accessible

### Reset Everything
```bash
# Stop and remove all containers
docker-compose down -v

# Remove all images
docker-compose down --rmi all

# Start fresh
docker-compose up -d --build
```

## License

MIT License - see LICENSE file for details.
