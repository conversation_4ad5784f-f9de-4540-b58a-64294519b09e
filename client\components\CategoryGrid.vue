<template>
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
    <div 
      v-for="category in displayCategories" 
      :key="category.id"
      class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow duration-300 cursor-pointer"
      @click="navigateToCategory(category.slug)"
    >
      <figure class="h-48">
        <NuxtImg 
          :src="category.image || 'https://img.daisyui.com/images/stock/photo-1606107557195-0e29a4b5b4aa.webp'" 
          :alt="category.name"
          class="w-full h-full object-cover"
        />
      </figure>
      
      <div class="card-body p-4 text-center">
        <!-- Category Title -->
        <h3 class="card-title justify-center text-lg">{{ category.name }}</h3>
        
        <!-- Description -->
        <p v-if="category.description" class="text-base-content/70 text-sm">
          {{ category.description }}
        </p>
        
        <!-- Product Count -->
        <p v-if="category._count?.products" class="text-xs text-base-content/50">
          {{ category._count.products }} {{ category._count.products === 1 ? 'product' : 'products' }}
        </p>
        
        <!-- CTA Button -->
        <div class="card-actions justify-center mt-4">
          <button class="btn btn-primary btn-sm">
            {{ category.buttonText || 'Shop Now' }}
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const categoriesStore = useCategoriesStore()

// Get categories from store
const storeCategories = computed(() => categoriesStore.activeCategories)

// Default categories if none are loaded
const defaultCategories = [
  {
    id: 'default-1',
    name: 'Electronics',
    slug: 'electronics',
    description: 'Latest electronic devices and gadgets',
    buttonText: 'Shop Electronics',
    image: 'https://img.daisyui.com/images/stock/photo-1606107557195-0e29a4b5b4aa.webp',
    _count: { products: 25 }
  },
  {
    id: 'default-2',
    name: 'Clothing',
    slug: 'clothing',
    description: 'Fashion and apparel for all occasions',
    buttonText: 'Shop Clothing',
    image: 'https://img.daisyui.com/images/stock/photo-1572635148818-ef6fd45eb394.webp',
    _count: { products: 18 }
  },
  {
    id: 'default-3',
    name: 'Home & Garden',
    slug: 'home-garden',
    description: 'Everything for your home and garden',
    buttonText: 'Shop Home & Garden',
    image: 'https://img.daisyui.com/images/stock/photo-1494253109108-2e30c049369b.webp',
    _count: { products: 32 }
  },
  {
    id: 'default-4',
    name: 'Sports & Outdoors',
    slug: 'sports-outdoors',
    description: 'Gear for sports and outdoor activities',
    buttonText: 'Shop Sports',
    image: 'https://img.daisyui.com/images/stock/photo-1550258987-190a2d41a8ba.webp',
    _count: { products: 14 }
  }
]

// Use default categories if no categories are available
const displayCategories = computed(() => {
  return storeCategories.value.length > 0 ? storeCategories.value.slice(0, 4) : defaultCategories
})

// Navigate to category
const navigateToCategory = (slug) => {
  navigateTo(`/categories/${slug}`)
}
</script>
