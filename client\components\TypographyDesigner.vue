<template>
  <div class="space-y-8">
    <!-- Section Header -->
    <div class="border-b pb-4">
      <h2 class="text-2xl font-semibold">Typography Design</h2>
      <p class="text-base-content/70 mt-1">
        Customize heading styles throughout your website
      </p>
    </div>

    <!-- Typography Settings -->
    <div class="space-y-6">
      <div
        v-for="(heading, key) in localDesign"
        :key="key"
        class="card bg-base-100 shadow-sm border"
      >
        <div class="card-body">
          <h3 class="card-title text-lg">{{ key.toUpperCase() }} Styling</h3>

          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Font Family -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Font Family</span>
              </label>
              <select
                v-model="localDesign[key].fontFamily"
                class="select select-bordered select-sm"
                @change="updateDesign"
              >
                <option value="Inter">Inter</option>
                <option value="Roboto">Roboto</option>
                <option value="Open Sans">Open Sans</option>
                <option value="Montserrat">Montserrat</option>
                <option value="Poppins">Poppins</option>
              </select>
            </div>

            <!-- Font Size -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Font Size</span>
              </label>
              <input
                v-model.number="localDesign[key].fontSize"
                type="number"
                min="12"
                max="72"
                class="input input-bordered input-sm"
                @input="updateDesign"
              />
            </div>

            <!-- Font Weight -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Font Weight</span>
              </label>
              <select
                v-model="localDesign[key].fontWeight"
                class="select select-bordered select-sm"
                @change="updateDesign"
              >
                <option value="300">Light</option>
                <option value="400">Normal</option>
                <option value="500">Medium</option>
                <option value="600">Semi Bold</option>
                <option value="700">Bold</option>
                <option value="800">Extra Bold</option>
                <option value="900">Black</option>
              </select>
            </div>

            <!-- Color -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Color</span>
              </label>
              <input
                v-model="localDesign[key].color"
                type="color"
                class="input input-bordered w-full h-10"
                @input="updateDesign"
              />
            </div>
          </div>

          <!-- Preview -->
          <div class="mt-4 p-4 bg-base-200 rounded-lg">
            <component
              :is="key"
              :style="{
                fontFamily: localDesign[key].fontFamily,
                fontSize: `${localDesign[key].fontSize}px`,
                fontWeight: localDesign[key].fontWeight,
                color: localDesign[key].color,
              }"
            >
              Sample {{ key.toUpperCase() }} Text
            </component>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  design: {
    type: Object,
    required: true,
  },
});

// Emits
const emit = defineEmits(["update:design", "update"]);

// Local state
const localDesign = ref({
  h1: {
    fontFamily: "Inter",
    fontSize: 48,
    fontWeight: "700",
    color: "#000000",
  },
  h2: {
    fontFamily: "Inter",
    fontSize: 36,
    fontWeight: "600",
    color: "#000000",
  },
  h3: {
    fontFamily: "Inter",
    fontSize: 24,
    fontWeight: "600",
    color: "#000000",
  },
  h4: {
    fontFamily: "Inter",
    fontSize: 20,
    fontWeight: "500",
    color: "#000000",
  },
  ...(props.design || {}),
});

// Methods
const updateDesign = () => {
  emit("update:design", { ...localDesign.value });
  emit("update");
};

// Watch for prop changes
watch(
  () => props.design,
  (newDesign) => {
    localDesign.value = { ...newDesign };
  },
  { deep: true }
);
</script>
