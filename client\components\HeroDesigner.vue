<template>
  <div class="space-y-8">
    <!-- Section Header -->
    <div class="border-b pb-4">
      <h2 class="text-2xl font-semibold">Hero Section Design</h2>
      <p class="text-base-content/70 mt-1">
        Customize your hero carousel appearance and content
      </p>
    </div>

    <!-- Carousel Dimensions -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h3 class="card-title text-lg">Carousel Dimensions</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Width Settings -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Width</span>
            </label>
            <select
              v-model="localDesign.width"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="full">Full Width</option>
              <option value="1920">Ultra Wide (1920px)</option>
              <option value="1600">Wide (1600px)</option>
              <option value="1440">Large Desktop (1440px)</option>
              <option value="1200">Desktop (1200px)</option>
              <option value="1024">Tablet Landscape (1024px)</option>
              <option value="768">Tablet (768px)</option>
              <option value="640">Mobile Large (640px)</option>
            </select>
          </div>

          <!-- Height Settings -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Height</span>
            </label>
            <select
              v-model="localDesign.height"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="100vh">Full Viewport (100vh)</option>
              <option value="80vh">80% Viewport (80vh)</option>
              <option value="60vh">60% Viewport (60vh)</option>
              <option value="500px">Large (500px)</option>
              <option value="400px">Medium (400px)</option>
              <option value="300px">Small (300px)</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Spacing Controls -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h3 class="card-title text-lg">Spacing</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Padding -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Padding</span>
            </label>
            <div class="grid grid-cols-2 gap-2">
              <div class="form-control">
                <label class="label py-1">
                  <span class="label-text-alt">Top</span>
                </label>
                <input
                  v-model.number="localDesign.padding.top"
                  type="number"
                  min="0"
                  max="100"
                  class="input input-bordered input-sm"
                  @input="updateDesign"
                />
              </div>
              <div class="form-control">
                <label class="label py-1">
                  <span class="label-text-alt">Right</span>
                </label>
                <input
                  v-model.number="localDesign.padding.right"
                  type="number"
                  min="0"
                  max="100"
                  class="input input-bordered input-sm"
                  @input="updateDesign"
                />
              </div>
              <div class="form-control">
                <label class="label py-1">
                  <span class="label-text-alt">Bottom</span>
                </label>
                <input
                  v-model.number="localDesign.padding.bottom"
                  type="number"
                  min="0"
                  max="100"
                  class="input input-bordered input-sm"
                  @input="updateDesign"
                />
              </div>
              <div class="form-control">
                <label class="label py-1">
                  <span class="label-text-alt">Left</span>
                </label>
                <input
                  v-model.number="localDesign.padding.left"
                  type="number"
                  min="0"
                  max="100"
                  class="input input-bordered input-sm"
                  @input="updateDesign"
                />
              </div>
            </div>
          </div>

          <!-- Margin -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Margin</span>
            </label>
            <div class="grid grid-cols-2 gap-2">
              <div class="form-control">
                <label class="label py-1">
                  <span class="label-text-alt">Top</span>
                </label>
                <input
                  v-model.number="localDesign.margin.top"
                  type="number"
                  min="0"
                  max="100"
                  class="input input-bordered input-sm"
                  @input="updateDesign"
                />
              </div>
              <div class="form-control">
                <label class="label py-1">
                  <span class="label-text-alt">Right</span>
                </label>
                <input
                  v-model.number="localDesign.margin.right"
                  type="number"
                  min="0"
                  max="100"
                  class="input input-bordered input-sm"
                  @input="updateDesign"
                />
              </div>
              <div class="form-control">
                <label class="label py-1">
                  <span class="label-text-alt">Bottom</span>
                </label>
                <input
                  v-model.number="localDesign.margin.bottom"
                  type="number"
                  min="0"
                  max="100"
                  class="input input-bordered input-sm"
                  @input="updateDesign"
                />
              </div>
              <div class="form-control">
                <label class="label py-1">
                  <span class="label-text-alt">Left</span>
                </label>
                <input
                  v-model.number="localDesign.margin.left"
                  type="number"
                  min="0"
                  max="100"
                  class="input input-bordered input-sm"
                  @input="updateDesign"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Background Settings -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h3 class="card-title text-lg">Background</h3>

        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text font-medium">Background Type</span>
          </label>
          <div class="flex gap-4">
            <label class="label cursor-pointer">
              <input
                v-model="localDesign.background.type"
                type="radio"
                value="color"
                class="radio radio-primary"
                @change="updateDesign"
              />
              <span class="label-text ml-2">Solid Color</span>
            </label>
            <label class="label cursor-pointer">
              <input
                v-model="localDesign.background.type"
                type="radio"
                value="gradient"
                class="radio radio-primary"
                @change="updateDesign"
              />
              <span class="label-text ml-2">Gradient</span>
            </label>
          </div>
        </div>

        <!-- Solid Color -->
        <div
          v-if="localDesign.background.type === 'color'"
          class="form-control"
        >
          <label class="label">
            <span class="label-text font-medium">Background Color</span>
          </label>
          <input
            v-model="localDesign.background.color"
            type="color"
            class="input input-bordered w-20 h-12"
            @input="updateDesign"
          />
        </div>

        <!-- Gradient -->
        <div
          v-else-if="localDesign.background.type === 'gradient'"
          class="space-y-4"
        >
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">From Color</span>
              </label>
              <input
                v-model="localDesign.background.gradient.from"
                type="color"
                class="input input-bordered w-20 h-12"
                @input="updateDesign"
              />
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">To Color</span>
              </label>
              <input
                v-model="localDesign.background.gradient.to"
                type="color"
                class="input input-bordered w-20 h-12"
                @input="updateDesign"
              />
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Direction</span>
              </label>
              <select
                v-model="localDesign.background.gradient.direction"
                class="select select-bordered"
                @change="updateDesign"
              >
                <option value="to-r">Left to Right</option>
                <option value="to-l">Right to Left</option>
                <option value="to-b">Top to Bottom</option>
                <option value="to-t">Bottom to Top</option>
                <option value="to-br">Top-Left to Bottom-Right</option>
                <option value="to-bl">Top-Right to Bottom-Left</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Carousel Images -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h3 class="card-title text-lg">Carousel Slides</h3>
        <p class="text-sm text-base-content/70 mb-4">
          Create slides with or without images
        </p>

        <!-- Slide Management -->
        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text font-medium">Add Slides</span>
          </label>
          <div class="flex gap-2">
            <button
              @click="addSlideWithoutImage"
              class="btn btn-outline btn-sm"
            >
              <Icon name="heroicons:plus" class="w-4 h-4" />
              Add Slide
            </button>
            <input
              type="file"
              multiple
              accept="image/*"
              class="file-input file-input-bordered file-input-sm flex-1"
              @change="handleImageUpload"
            />
          </div>
          <div class="label">
            <span class="label-text-alt"
              >Add slides with or without images</span
            >
          </div>
        </div>

        <!-- Slide List -->
        <div v-if="localDesign.slides.length > 0" class="space-y-2">
          <div
            v-for="(slide, index) in localDesign.slides"
            :key="index"
            class="flex items-start gap-4 p-3 border rounded-lg"
          >
            <!-- Image or placeholder -->
            <div class="w-16 h-16 flex-shrink-0">
              <NuxtImg
                v-if="slide.image"
                :src="slide.image"
                :alt="`Slide ${index + 1}`"
                class="w-16 h-16 object-cover rounded"
              />
              <div
                v-else
                class="w-16 h-16 bg-base-200 rounded flex items-center justify-center"
              >
                <Icon
                  name="heroicons:photo"
                  class="w-6 h-6 text-base-content/50"
                />
              </div>
            </div>

            <!-- Slide content -->
            <div class="flex-1 space-y-2">
              <input
                v-model="slide.title"
                type="text"
                placeholder="Slide title"
                class="input input-bordered input-sm w-full"
                @input="updateDesign"
              />
              <input
                v-model="slide.subtitle"
                type="text"
                placeholder="Slide subtitle"
                class="input input-bordered input-sm w-full"
                @input="updateDesign"
              />
              <div class="flex gap-2">
                <input
                  v-model="slide.buttonText"
                  type="text"
                  placeholder="Button text"
                  class="input input-bordered input-sm flex-1"
                  @input="updateDesign"
                />
                <input
                  v-model="slide.link"
                  type="text"
                  placeholder="Button link (optional)"
                  class="input input-bordered input-sm flex-1"
                  @input="updateDesign"
                />
              </div>
            </div>
            <div class="flex gap-2">
              <button
                @click="moveSlide(index, -1)"
                :disabled="index === 0"
                class="btn btn-sm btn-ghost"
              >
                <Icon name="heroicons:arrow-up" class="h-4 w-4" />
              </button>
              <button
                @click="moveSlide(index, 1)"
                :disabled="index === localDesign.slides.length - 1"
                class="btn btn-sm btn-ghost"
              >
                <Icon name="heroicons:arrow-down" class="h-4 w-4" />
              </button>
              <button
                @click="removeSlide(index)"
                class="btn btn-sm btn-error btn-ghost"
              >
                <Icon name="heroicons:trash" class="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        <!-- Image Positioning -->
        <div class="form-control mt-4">
          <label class="label">
            <span class="label-text font-medium">Image Positioning</span>
          </label>
          <select
            v-model="localDesign.imagePosition"
            class="select select-bordered"
            @change="updateDesign"
          >
            <option value="cover">Cover (Fill entire slide)</option>
            <option value="contain">Contain (Fit within slide)</option>
            <option value="left">Left Side</option>
            <option value="right">Right Side</option>
            <option value="top-left">Top Left</option>
            <option value="top-right">Top Right</option>
            <option value="bottom-left">Bottom Left</option>
            <option value="bottom-right">Bottom Right</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Text Design -->
    <CarouselTextDesigner
      :design="localDesign.textDesign"
      @update:design="updateTextDesign"
    />

    <!-- Button Design -->
    <CarouselButtonDesigner
      :design="localDesign.buttonDesign"
      @update:design="updateButtonDesign"
    />

    <!-- Preview -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h3 class="card-title text-lg">Preview</h3>
        <div class="mockup-browser border bg-base-300">
          <div class="mockup-browser-toolbar">
            <div class="input">https://yourstore.com</div>
          </div>
          <div class="bg-base-200">
            <HeroPreview :design="localDesign" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Default design settings
const getDefaultDesign = () => ({
  width: "full",
  height: "500px",
  padding: { top: 0, right: 0, bottom: 0, left: 0 },
  margin: { top: 0, right: 0, bottom: 0, left: 0 },
  background: {
    type: "color",
    color: "#ffffff",
    gradient: { from: "#ffffff", to: "#f3f4f6", direction: "to-r" },
  },
  slides: [],
  imagePosition: "cover",
  textDesign: {
    title: {
      fontFamily: "Inter",
      fontSize: 48,
      fontWeight: "700",
      color: "#ffffff",
    },
    subtitle: {
      fontFamily: "Inter",
      fontSize: 18,
      fontWeight: "400",
      color: "#ffffff",
    },
  },
  buttonDesign: {
    style: "primary",
    size: "lg",
    rounded: "default",
    wide: false,
    block: false,
    square: false,
    backgroundColor: "#3b82f6",
    textColor: "#ffffff",
    borderColor: "#3b82f6",
  },
});

// Props
const props = defineProps({
  design: {
    type: Object,
    required: true,
  },
});

// Emits
const emit = defineEmits(["update:design", "update"]);

// Local state
const localDesign = ref({ ...getDefaultDesign(), ...(props.design || {}) });

// Methods
const updateDesign = () => {
  emit("update:design", { ...localDesign.value });
  emit("update");
};

const updateTextDesign = (textDesign) => {
  localDesign.value.textDesign = textDesign;
  updateDesign();
};

const updateButtonDesign = (buttonDesign) => {
  localDesign.value.buttonDesign = buttonDesign;
  updateDesign();
};

const handleImageUpload = async (event) => {
  const files = Array.from(event.target.files);
  const maxImages = 4;

  if (localDesign.value.slides.length + files.length > maxImages) {
    alert(`You can only upload up to ${maxImages} images`);
    return;
  }

  for (const file of files) {
    try {
      // Upload to server
      const api = useApi();
      const formData = new FormData();
      formData.append("image", file);

      const response = await api.upload(
        "/admin/content/upload-carousel-image",
        formData,
        {
          showLoading: true,
          loadingMessage: "Uploading image...",
        }
      );

      localDesign.value.slides.push({
        id: Date.now() + Math.random(),
        image: response.imageUrl,
        title: "",
        subtitle: "",
        link: "",
        buttonText: "",
      });
    } catch (error) {
      console.error("Error uploading image:", error);
      // Show error message instead of using blob URL fallback
      const { $toast } = useNuxtApp();
      $toast.error(`Failed to upload image: ${file.name}`);
      // Don't add the slide if upload failed
    }
  }

  updateDesign();
};

const addSlideWithoutImage = () => {
  localDesign.value.slides.push({
    id: Date.now() + Math.random(),
    image: null,
    title: "",
    subtitle: "",
    link: "",
    buttonText: "",
  });
  updateDesign();
};

const moveSlide = (index, direction) => {
  const newIndex = index + direction;
  if (newIndex >= 0 && newIndex < localDesign.value.slides.length) {
    const slides = [...localDesign.value.slides];
    [slides[index], slides[newIndex]] = [slides[newIndex], slides[index]];
    localDesign.value.slides = slides;
    updateDesign();
  }
};

const removeSlide = (index) => {
  localDesign.value.slides.splice(index, 1);
  updateDesign();
};

// Watch for prop changes
watch(
  () => props.design,
  (newDesign) => {
    if (newDesign) {
      localDesign.value = { ...getDefaultDesign(), ...newDesign };
    }
  },
  { deep: true }
);
</script>
