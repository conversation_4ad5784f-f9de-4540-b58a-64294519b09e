<!--  client/components/HeroCarousel.vue -->
<template>
  <!-- Carousel Container with margins and rounded corners -->
  <div :class="containerClasses" :style="containerStyles">
    <div :class="carouselClasses" :style="carouselStyles">
      <div
        v-for="(slide, index) in displaySlides"
        :key="slide.id"
        :id="`slide${index + 1}`"
        class="carousel-item relative w-full"
      >
        <NuxtImg
          v-if="slide.image"
          :src="slide.image"
          :alt="slide.title || 'Hero slide'"
          class="w-full object-cover rounded-box"
        />

        <!-- Overlay content -->
        <div
          class="absolute inset-0 flex items-center justify-center rounded-box"
          :class="slide.image ? 'bg-black bg-opacity-40' : ''"
        >
          <div
            class="text-center max-w-2xl px-4"
            :class="slide.image ? 'text-white' : 'text-gray-800'"
          >
            <h1 class="text-4xl md:text-6xl font-bold mb-4">
              {{ slide.title || getDefaultTitle(index) }}
            </h1>
            <p class="text-lg md:text-xl mb-8">
              {{ slide.subtitle || getDefaultSubtitle() }}
            </p>
            <NuxtLink
              v-if="slide.link"
              :to="slide.link"
              class="btn btn-primary btn-lg"
            >
              Shop Now
            </NuxtLink>
          </div>
        </div>

        <!-- Navigation arrows - positioned at top right -->
        <div class="absolute top-4 right-4 flex gap-2">
          <button
            @click="goToSlide(index === 0 ? displaySlides.length : index)"
            class="btn btn-circle btn-sm btn-ghost bg-white bg-opacity-20 text-white hover:bg-white hover:bg-opacity-30 backdrop-blur-sm"
            title="Previous slide"
          >
            <Icon name="heroicons:chevron-left" class="h-4 w-4" />
          </button>
          <button
            @click="
              goToSlide(index === displaySlides.length - 1 ? 1 : index + 2)
            "
            class="btn btn-circle btn-sm btn-ghost bg-white bg-opacity-20 text-white hover:bg-white hover:bg-opacity-30 backdrop-blur-sm"
            title="Next slide"
          >
            <Icon name="heroicons:chevron-right" class="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Indicator dots -->
  <!-- <div
    v-if="displaySlides.length > 1"
    class="flex w-full justify-center gap-2 py-2"
  >
    <a
      v-for="(slide, index) in displaySlides"
      :key="`indicator-${slide.id}`"
      :href="`#slide${index + 1}`"
      class="btn btn-xs"
      :class="{ 'btn-primary': index === 0, 'btn-outline': index !== 0 }"
    >
      {{ index + 1 }}
    </a>
  </div> -->
</template>

<script setup>
const contentStore = useContentStore();

// Get carousel slides from store
const storeSlides = computed(() => contentStore.activeCarouselSlides);

// Get homepage design settings
const homepageDesign = computed(() => contentStore.homepageDesign);
const heroDesign = computed(() => homepageDesign.value?.heroDesign);

// Default slides if none are loaded
const defaultSlides = [
  {
    id: "default-1",
    title: "",
    subtitle: "",
    image: null,
    link: null,
  },
  {
    id: "default-2",
    title: "",
    subtitle: "",
    image: null,
    link: null,
  },
  {
    id: "default-3",
    title: "",
    subtitle: "",
    image: null,
    link: null,
  },
];

// Use default slides if no slides are available
const displaySlides = computed(() => {
  return storeSlides.value.length > 0 ? storeSlides.value : defaultSlides;
});

// Helper functions for default titles and subtitles
const getDefaultTitle = (index) => {
  return `Slide ${index + 1}`;
};

const getDefaultSubtitle = () => {
  return "Here you should edit background colors, or upload an image, and then add the title and subtitles and cta button.";
};

// Navigation method that prevents scrolling
const goToSlide = (slideNumber) => {
  const targetSlide = document.getElementById(`slide${slideNumber}`);
  if (targetSlide) {
    targetSlide.scrollIntoView({
      behavior: "smooth",
      block: "nearest",
      inline: "start",
    });
  }
};

// Hero styling from design settings
const containerClasses = computed(() => {
  if (!heroDesign.value) return "mx-auto px-4 md:px-8 mt-6 mb-8";

  const classes = [];

  // Width classes
  switch (heroDesign.value.width) {
    case "container":
      classes.push("max-w-7xl mx-auto px-4");
      break;
    case "lg":
      classes.push("max-w-5xl mx-auto px-4");
      break;
    case "md":
      classes.push("max-w-3xl mx-auto px-4");
      break;
    case "sm":
      classes.push("max-w-2xl mx-auto px-4");
      break;
    default:
      classes.push("mx-auto px-4 md:px-8");
      break;
  }

  classes.push("mt-6 mb-8"); // Default spacing

  return classes.join(" ");
});

const containerStyles = computed(() => {
  if (!heroDesign.value) return {};

  const styles = {};

  // Margin - only apply top and bottom margins to preserve horizontal centering
  if (heroDesign.value.margin) {
    const m = heroDesign.value.margin;
    styles.marginTop = `${m.top || 0}px`;
    styles.marginBottom = `${m.bottom || 0}px`;
    // Don't apply marginRight and marginLeft as they would override mx-auto centering
  }

  return styles;
});

const carouselClasses = computed(() => {
  return "carousel carousel-center rounded-box w-full shadow-lg";
});

const carouselStyles = computed(() => {
  if (!heroDesign.value) return { height: "500px" };

  const styles = {
    height: heroDesign.value.height || "500px",
  };

  // Padding
  if (heroDesign.value.padding) {
    const p = heroDesign.value.padding;
    styles.paddingTop = `${p.top || 0}px`;
    styles.paddingRight = `${p.right || 0}px`;
    styles.paddingBottom = `${p.bottom || 0}px`;
    styles.paddingLeft = `${p.left || 0}px`;
  }

  // Background
  if (heroDesign.value.background) {
    const bg = heroDesign.value.background;
    if (bg.type === "gradient") {
      const direction = bg.gradient.direction;
      // Convert direction to proper CSS linear-gradient syntax
      const cssDirection = direction
        .replace("to-r", "to right")
        .replace("to-l", "to left")
        .replace("to-b", "to bottom")
        .replace("to-t", "to top")
        .replace("to-br", "to bottom right")
        .replace("to-bl", "to bottom left");

      styles.background = `linear-gradient(${cssDirection}, ${bg.gradient.from}, ${bg.gradient.to})`;
    } else {
      styles.backgroundColor = bg.color;
    }
  }

  return styles;
});
</script>
