<!--  client/components/HeroCarousel.vue -->
<template>
  <!-- Carousel Container with margins and rounded corners -->
  <div :class="containerClasses" :style="containerStyles">
    <div :class="carouselClasses" :style="carouselStyles">
      <div
        v-for="(slide, index) in displaySlides"
        :key="slide.id"
        :id="`slide${index + 1}`"
        class="carousel-item relative w-full"
      >
        <NuxtImg
          v-if="slide.image"
          :src="slide.image"
          :alt="slide.title || 'Hero slide'"
          class="w-full object-cover rounded-box"
        />

        <!-- Overlay content -->
        <div
          class="absolute inset-0 flex items-center rounded-box"
          :class="[
            slide.image ? 'bg-black bg-opacity-40' : '',
            getContentJustifyClass(slide.contentAlignment),
          ]"
        >
          <div class="max-w-2xl px-4 text-center">
            <h1 class="carousel-title mb-4" :style="titleStyles">
              {{ slide.title || getDefaultTitle(index) }}
            </h1>
            <p class="carousel-subtitle mb-8" :style="subtitleStyles">
              {{ slide.subtitle || getDefaultSubtitle() }}
            </p>
            <NuxtLink
              v-if="slide.link"
              :to="slide.link"
              :class="`carousel-button ${buttonClasses}`"
              :style="buttonStyles"
            >
              {{ slide.buttonText || "Shop Now" }}
            </NuxtLink>
            <button
              v-else
              :class="`carousel-button ${buttonClasses}`"
              :style="buttonStyles"
              disabled
              class="opacity-50 cursor-not-allowed"
            >
              {{ slide.buttonText || "Shop Now" }}
            </button>
          </div>
        </div>

        <!-- Navigation arrows - positioned at top right -->
        <div class="absolute top-4 right-4 flex gap-2">
          <button
            @click="goToSlide(index === 0 ? displaySlides.length : index)"
            class="btn btn-circle btn-sm btn-ghost bg-white bg-opacity-20 text-white hover:bg-white hover:bg-opacity-30 backdrop-blur-sm"
            title="Previous slide"
          >
            <Icon name="heroicons:chevron-left" class="h-4 w-4" />
          </button>
          <button
            @click="
              goToSlide(index === displaySlides.length - 1 ? 1 : index + 2)
            "
            class="btn btn-circle btn-sm btn-ghost bg-white bg-opacity-20 text-white hover:bg-white hover:bg-opacity-30 backdrop-blur-sm"
            title="Next slide"
          >
            <Icon name="heroicons:chevron-right" class="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Indicator dots -->
  <!-- <div
    v-if="displaySlides.length > 1"
    class="flex w-full justify-center gap-2 py-2"
  >
    <a
      v-for="(slide, index) in displaySlides"
      :key="`indicator-${slide.id}`"
      :href="`#slide${index + 1}`"
      class="btn btn-xs"
      :class="{ 'btn-primary': index === 0, 'btn-outline': index !== 0 }"
    >
      {{ index + 1 }}
    </a>
  </div> -->
</template>

<script setup>
const contentStore = useContentStore();

// Get homepage design settings
const homepageDesign = computed(() => contentStore.homepageDesign);
const heroDesign = computed(() => homepageDesign.value?.heroDesign);

// Default slides if none are loaded
const defaultSlides = [
  {
    id: "default-1",
    title: "",
    subtitle: "",
    image: null,
    link: null,
    buttonText: "",
  },
  {
    id: "default-2",
    title: "",
    subtitle: "",
    image: null,
    link: null,
    buttonText: "",
  },
  {
    id: "default-3",
    title: "",
    subtitle: "",
    image: null,
    link: null,
    buttonText: "",
  },
];

// Use slides from hero design settings, fallback to default slides
const displaySlides = computed(() => {
  const heroSlides = heroDesign.value?.slides || [];
  return heroSlides.length > 0 ? heroSlides : defaultSlides;
});

// Helper functions for default titles and subtitles
const getDefaultTitle = (index) => {
  return `Slide ${index + 1}`;
};

const getDefaultSubtitle = () => {
  return "Here you should edit background colors, or upload an image, and then add the title and subtitles and cta button.";
};

// Navigation method that prevents scrolling
const goToSlide = (slideNumber) => {
  const targetSlide = document.getElementById(`slide${slideNumber}`);
  if (targetSlide) {
    targetSlide.scrollIntoView({
      behavior: "smooth",
      block: "nearest",
      inline: "start",
    });
  }
};

// Hero styling from design settings
const containerClasses = computed(() => {
  if (!heroDesign.value) return "mx-auto px-4 md:px-8 mt-6 mb-8";

  const classes = [];

  // Width classes - using exact pixel values for consistency with navbar
  switch (heroDesign.value.width) {
    case "1920":
      classes.push("mx-auto px-4");
      classes.push("max-w-[1920px]");
      break;
    case "1600":
      classes.push("mx-auto px-4");
      classes.push("max-w-[1600px]");
      break;
    case "1440":
      classes.push("mx-auto px-4");
      classes.push("max-w-[1440px]");
      break;
    case "1200":
      classes.push("mx-auto px-4");
      classes.push("max-w-[1200px]");
      break;
    case "1024":
      classes.push("mx-auto px-4");
      classes.push("max-w-[1024px]");
      break;
    case "768":
      classes.push("mx-auto px-4");
      classes.push("max-w-[768px]");
      break;
    case "640":
      classes.push("mx-auto px-4");
      classes.push("max-w-[640px]");
      break;
    default:
      classes.push("mx-auto px-4 md:px-8");
      break;
  }

  classes.push("mt-6 mb-8"); // Default spacing

  return classes.join(" ");
});

const containerStyles = computed(() => {
  if (!heroDesign.value) return {};

  const styles = {};

  // Margin - only apply top and bottom margins to preserve horizontal centering
  if (heroDesign.value.margin) {
    const m = heroDesign.value.margin;
    styles.marginTop = `${m.top || 0}px`;
    styles.marginBottom = `${m.bottom || 0}px`;
    // Don't apply marginRight and marginLeft as they would override mx-auto centering
  }

  return styles;
});

const carouselClasses = computed(() => {
  return "carousel carousel-center rounded-box w-full shadow-lg";
});

const carouselStyles = computed(() => {
  if (!heroDesign.value) return { height: "500px" };

  const styles = {
    height: heroDesign.value.height || "500px",
  };

  // Padding
  if (heroDesign.value.padding) {
    const p = heroDesign.value.padding;
    styles.paddingTop = `${p.top || 0}px`;
    styles.paddingRight = `${p.right || 0}px`;
    styles.paddingBottom = `${p.bottom || 0}px`;
    styles.paddingLeft = `${p.left || 0}px`;
  }

  // Background
  if (heroDesign.value.background) {
    const bg = heroDesign.value.background;
    if (bg.type === "gradient") {
      const direction = bg.gradient.direction;
      // Convert direction to proper CSS linear-gradient syntax
      const cssDirection = direction
        .replace("to-r", "to right")
        .replace("to-l", "to left")
        .replace("to-b", "to bottom")
        .replace("to-t", "to top")
        .replace("to-br", "to bottom right")
        .replace("to-bl", "to bottom left");

      styles.background = `linear-gradient(${cssDirection}, ${bg.gradient.from}, ${bg.gradient.to})`;
    } else {
      styles.backgroundColor = bg.color;
    }
  }

  return styles;
});

// Text and button styling
const titleStyles = computed(() => {
  const textDesign = heroDesign.value?.textDesign?.title;
  if (!textDesign) {
    // DaisyUI defaults for title
    return {
      fontFamily: "Inter",
      fontSize: "3rem", // text-4xl equivalent
      fontWeight: "700",
      color: "#ffffff",
    };
  }

  return {
    fontFamily: textDesign.fontFamily,
    fontSize: `${textDesign.fontSize}px`,
    fontWeight: textDesign.fontWeight,
    color: textDesign.color,
  };
});

const subtitleStyles = computed(() => {
  const textDesign = heroDesign.value?.textDesign?.subtitle;
  if (!textDesign) {
    // DaisyUI defaults for subtitle
    return {
      fontFamily: "Inter",
      fontSize: "1.125rem", // text-lg equivalent
      fontWeight: "400",
      color: "#ffffff",
    };
  }

  return {
    fontFamily: textDesign.fontFamily,
    fontSize: `${textDesign.fontSize}px`,
    fontWeight: textDesign.fontWeight,
    color: textDesign.color,
  };
});

const buttonClasses = computed(() => {
  const buttonDesign = heroDesign.value?.buttonDesign;
  if (!buttonDesign) {
    return "btn btn-primary btn-lg"; // DaisyUI default
  }

  const classes = ["btn"];

  // Style classes
  if (buttonDesign.style !== "custom") {
    switch (buttonDesign.style) {
      case "primary":
        classes.push("btn-primary");
        break;
      case "secondary":
        classes.push("btn-secondary");
        break;
      case "accent":
        classes.push("btn-accent");
        break;
      case "neutral":
        classes.push("btn-neutral");
        break;
      case "info":
        classes.push("btn-info");
        break;
      case "success":
        classes.push("btn-success");
        break;
      case "warning":
        classes.push("btn-warning");
        break;
      case "error":
        classes.push("btn-error");
        break;
      case "outline":
        classes.push("btn-outline");
        break;
      case "soft":
        classes.push("btn-soft");
        break;
      case "dash":
        classes.push("btn-dash");
        break;
      case "ghost":
        classes.push("btn-ghost");
        break;
      case "link":
        classes.push("btn-link");
        break;
      default:
        classes.push("btn-primary");
        break;
    }
  }

  // Size classes
  switch (buttonDesign.size) {
    case "xs":
      classes.push("btn-xs");
      break;
    case "sm":
      classes.push("btn-sm");
      break;
    case "lg":
      classes.push("btn-lg");
      break;
    case "xl":
      classes.push("btn-xl");
      break;
    // md is default, no class needed
  }

  // Rounded corner classes
  switch (buttonDesign.rounded) {
    case "none":
      classes.push("rounded-none");
      break;
    case "sm":
      classes.push("rounded-sm");
      break;
    case "md":
      classes.push("rounded-md");
      break;
    case "lg":
      classes.push("rounded-lg");
      break;
    case "xl":
      classes.push("rounded-xl");
      break;
    case "2xl":
      classes.push("rounded-2xl");
      break;
    case "3xl":
      classes.push("rounded-3xl");
      break;
    case "full":
      classes.push("rounded-full");
      break;
    case "circle":
      classes.push("btn-circle");
      break;
    // default uses DaisyUI default rounded
  }

  // Modifier classes
  if (buttonDesign.wide) {
    classes.push("btn-wide");
  }

  if (buttonDesign.block) {
    classes.push("btn-block");
  }

  if (buttonDesign.square) {
    classes.push("btn-square");
  }

  // Drop shadow control
  if (buttonDesign.dropShadow === true) {
    classes.push("shadow-lg", "!shadow-lg");
  } else {
    classes.push("shadow-none", "!shadow-none");
  }

  return classes.join(" ");
});

const buttonStyles = computed(() => {
  const buttonDesign = heroDesign.value?.buttonDesign;
  if (!buttonDesign || buttonDesign.style !== "custom") {
    return {}; // Use DaisyUI theme colors
  }

  return {
    backgroundColor: buttonDesign.backgroundColor,
    color: buttonDesign.textColor,
    borderColor: buttonDesign.borderColor,
  };
});

// Content alignment helper functions
const getContentJustifyClass = (alignment) => {
  switch (alignment) {
    case "left":
      return "justify-start";
    case "right":
      return "justify-end";
    case "center":
    default:
      return "justify-center";
  }
};
</script>
