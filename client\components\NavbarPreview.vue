<template>
  <div 
    :class="[
      'navbar bg-base-100',
      getHeightClass(),
      getPaddingClass(),
      getMarginClass()
    ]"
  >
    <div 
      :class="[
        'navbar-start',
        getAlignmentClass()
      ]"
    >
      <!-- Logo -->
      <div class="flex items-center">
        <div class="w-8 h-8 bg-primary rounded flex items-center justify-center mr-2">
          <span class="text-primary-content text-xs">Logo</span>
        </div>
        <span class="text-xl font-bold">Store</span>
      </div>
    </div>

    <div 
      :class="[
        'navbar-center',
        getAlignmentClass()
      ]"
    >
      <!-- Navigation Links -->
      <div 
        :class="[
          'flex',
          getSpacingClass()
        ]"
      >
        <a 
          v-for="link in sampleLinks" 
          :key="link"
          :class="[
            getLinkClass(),
            getHoverClass()
          ]"
          :style="getTextStyle()"
        >
          {{ link }}
        </a>
      </div>
    </div>

    <div class="navbar-end">
      <div class="flex items-center gap-2">
        <button class="btn btn-ghost btn-circle">
          <Icon name="heroicons:magnifying-glass" class="h-5 w-5" />
        </button>
        <button class="btn btn-ghost btn-circle">
          <Icon name="heroicons:shopping-cart" class="h-5 w-5" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  design: {
    type: Object,
    required: true
  }
});

// Sample navigation links
const sampleLinks = ['Home', 'Products', 'Categories', 'About', 'Contact'];

// Computed classes
const getHeightClass = () => {
  switch (props.design.height) {
    case 'compact': return 'h-12';
    case 'large': return 'h-20';
    case 'xl': return 'h-24';
    default: return 'h-16';
  }
};

const getPaddingClass = () => {
  const p = props.design.padding;
  return `pt-${p.top} pr-${p.right} pb-${p.bottom} pl-${p.left}`;
};

const getMarginClass = () => {
  const m = props.design.margin;
  return `mt-${m.top} mr-${m.right} mb-${m.bottom} ml-${m.left}`;
};

const getAlignmentClass = () => {
  switch (props.design.alignment) {
    case 'start': return 'justify-start';
    case 'end': return 'justify-end';
    default: return 'justify-center';
  }
};

const getSpacingClass = () => {
  switch (props.design.itemSpacing) {
    case 'tight': return 'gap-2';
    case 'relaxed': return 'gap-6';
    case 'loose': return 'gap-8';
    default: return 'gap-4';
  }
};

const getLinkClass = () => {
  const baseClass = 'btn';
  
  switch (props.design.linkStyle) {
    case 'ghost': return `${baseClass} btn-ghost`;
    case 'link': return 'link link-hover';
    case 'outline': return `${baseClass} btn-outline`;
    case 'underline': return 'link link-hover border-b-2 border-transparent';
    default: return `${baseClass} btn-ghost`;
  }
};

const getHoverClass = () => {
  switch (props.design.hoverStyle) {
    case 'scale': return 'hover:scale-105 transition-transform';
    case 'glow': return 'hover:shadow-lg transition-shadow';
    case 'underline': return 'hover:border-b-2 hover:border-primary';
    case 'background': return 'hover:bg-primary hover:text-primary-content';
    case 'none': return '';
    default: return 'hover:bg-base-200';
  }
};

const getTextStyle = () => {
  const styles = props.design.textStyles;
  return {
    fontFamily: styles.fontFamily,
    fontSize: `${styles.fontSize}px`,
    fontWeight: styles.fontWeight,
    color: styles.color
  };
};
</script>
