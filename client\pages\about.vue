<template>
  <div class="min-h-screen bg-base-100">
    <AppNavbar />
    
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Breadcrumbs -->
      <div class="breadcrumbs text-sm mb-8">
        <ul>
          <li><NuxtLink to="/">Home</NuxtLink></li>
          <li>About Us</li>
        </ul>
      </div>

      <!-- Hero Section -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold mb-4">About Multi Store</h1>
        <p class="text-xl text-base-content/70 max-w-2xl mx-auto">
          Your trusted partner for quality products and exceptional service since 2020.
        </p>
      </div>

      <!-- Story Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
        <div>
          <h2 class="text-2xl font-bold mb-4">Our Story</h2>
          <p class="text-base-content/80 mb-4">
            Multi Store was founded with a simple mission: to provide customers with access to high-quality products 
            at competitive prices, backed by exceptional customer service.
          </p>
          <p class="text-base-content/80 mb-4">
            What started as a small online retailer has grown into a comprehensive marketplace offering thousands 
            of products across multiple categories, serving customers worldwide.
          </p>
          <p class="text-base-content/80">
            We believe in building lasting relationships with our customers by consistently delivering on our 
            promises of quality, value, and service.
          </p>
        </div>
        <div class="flex justify-center">
          <div class="card bg-base-200 shadow-xl">
            <figure class="px-10 pt-10">
              <div class="w-64 h-48 bg-primary rounded-lg flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-primary-content" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
            </figure>
            <div class="card-body items-center text-center">
              <h3 class="card-title">Multi Store</h3>
              <p>Quality products, exceptional service</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Values Section -->
      <div class="mb-16">
        <h2 class="text-3xl font-bold text-center mb-8">Our Values</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="card bg-base-200 shadow-xl">
            <div class="card-body text-center">
              <div class="text-4xl mb-4">🎯</div>
              <h3 class="card-title justify-center">Quality First</h3>
              <p>We carefully curate our product selection to ensure every item meets our high standards.</p>
            </div>
          </div>
          
          <div class="card bg-base-200 shadow-xl">
            <div class="card-body text-center">
              <div class="text-4xl mb-4">💝</div>
              <h3 class="card-title justify-center">Customer Focus</h3>
              <p>Our customers are at the heart of everything we do. Your satisfaction is our priority.</p>
            </div>
          </div>
          
          <div class="card bg-base-200 shadow-xl">
            <div class="card-body text-center">
              <div class="text-4xl mb-4">🚀</div>
              <h3 class="card-title justify-center">Innovation</h3>
              <p>We continuously improve our platform and services to provide the best shopping experience.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Stats Section -->
      <div class="stats stats-vertical lg:stats-horizontal shadow w-full mb-16">
        <div class="stat">
          <div class="stat-figure text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
          <div class="stat-title">Happy Customers</div>
          <div class="stat-value text-primary">50K+</div>
          <div class="stat-desc">Worldwide</div>
        </div>

        <div class="stat">
          <div class="stat-figure text-secondary">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          </div>
          <div class="stat-title">Products</div>
          <div class="stat-value text-secondary">10K+</div>
          <div class="stat-desc">In our catalog</div>
        </div>

        <div class="stat">
          <div class="stat-figure text-accent">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div class="stat-title">Orders Delivered</div>
          <div class="stat-value text-accent">100K+</div>
          <div class="stat-desc">Successfully</div>
        </div>
      </div>

      <!-- Contact CTA -->
      <div class="card bg-gradient-to-r from-primary to-secondary text-primary-content shadow-xl">
        <div class="card-body text-center">
          <h2 class="card-title justify-center text-2xl mb-4">Get in Touch</h2>
          <p class="mb-6">
            Have questions or want to learn more about Multi Store? We'd love to hear from you.
          </p>
          <div class="card-actions justify-center">
            <NuxtLink to="/contact" class="btn btn-accent">
              Contact Us
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <AppFooter />
    <CartDrawer />
  </div>
</template>

<script setup>
// Meta tags
useHead({
  title: 'About Us - Multi Store',
  meta: [
    { name: 'description', content: 'Learn about Multi Store - your trusted partner for quality products and exceptional service.' }
  ]
})
</script>
