// client/stores/content.ts
import { defineStore } from "pinia";

interface CarouselSlide {
  id: string;
  title: string;
  description?: string;
  imageUrl: string;
  linkUrl?: string;
  isActive: boolean;
  order: number;
}

interface BenefitCard {
  id: string;
  title: string;
  description: string;
  iconName: string;
  isActive: boolean;
  order: number;
}

interface LimitedTimeOffer {
  id: string;
  title: string;
  description: string;
  discountPercentage?: number;
  endDate?: string;
  isActive: boolean;
}

interface LogoImageStyles {
  width: number;
  height: number;
  maintainAspectRatio: boolean;
  marginTop: number;
  marginRight: number;
  marginBottom: number;
  marginLeft: number;
  paddingTop: number;
  paddingRight: number;
  paddingBottom: number;
  paddingLeft: number;
}

interface LogoTextStyles {
  fontFamily: string;
  fontSize: number;
  fontWeight: string;
  fontStyle: string;
  color: string;
  marginTop: number;
  marginRight: number;
  marginBottom: number;
  marginLeft: number;
  paddingTop: number;
  paddingRight: number;
  paddingBottom: number;
  paddingLeft: number;
  verticalAlign: string;
}

interface CompanySettings {
  id: string;
  companyName: string;
  logoUrl?: string;
  logoImageStyles?: LogoImageStyles;
  logoText?: string;
  logoTextStyles?: LogoTextStyles;
  description?: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: string;
  companyEmail?: string;
  companyPhone?: string;
  companyAddress1?: string;
  companyAddress2?: string;
  companyCity?: string;
  companyState?: string;
  companyZip?: string;
  companyCountry?: string;
  facebookUrl?: string;
  twitterUrl?: string;
  instagramUrl?: string;
  linkedinUrl?: string;
  metaTitle?: string;
  metaDescription?: string;
  copyrightText?: string;
  newsletterTitle?: string;
  newsletterDescription?: string;
}

interface LegalPage {
  id: string;
  title: string;
  slug: string;
  content: string;
  isActive: boolean;
}

interface HomepageDesign {
  navbarDesign?: any;
  heroDesign?: any;
  benefitCardsDesign?: any;
  sectionBackgrounds?: any;
  cardsDesign?: any;
  typographyDesign?: any;
  pageDesign?: any;
  buttonDesign?: any;
}

interface ContentState {
  carouselSlides: CarouselSlide[];
  benefitCards: BenefitCard[];
  limitedTimeOffer: LimitedTimeOffer | null;
  categorySlideshow: CarouselSlide[];
  companySettings: CompanySettings | null;
  legalPages: Record<string, LegalPage>;
  homepageDesign: HomepageDesign;
  loading: boolean;
}

export const useContentStore = defineStore("content", {
  state: (): ContentState => ({
    carouselSlides: [],
    benefitCards: [],
    limitedTimeOffer: null,
    categorySlideshow: [],
    companySettings: null,
    legalPages: {},
    homepageDesign: {},
    loading: false,
  }),

  getters: {
    activeCarouselSlides: (state): CarouselSlide[] =>
      state.carouselSlides.filter((slide) => slide.isActive),
    activeBenefitCards: (state): BenefitCard[] =>
      state.benefitCards.filter((card) => card.isActive),
    activeCategorySlideshow: (state): CarouselSlide[] =>
      state.categorySlideshow.filter((slide) => slide.isActive),

    isLimitedTimeOfferActive: (state): boolean => {
      if (!state.limitedTimeOffer) return false;
      if (!state.limitedTimeOffer.endDate)
        return state.limitedTimeOffer.isActive;
      return (
        state.limitedTimeOffer.isActive &&
        new Date(state.limitedTimeOffer.endDate) > new Date()
      );
    },
  },

  actions: {
    async fetchCarouselSlides(): Promise<{ slides: CarouselSlide[] }> {
      try {
        const api = useApi();
        const response = await api.get<{ slides: CarouselSlide[] }>(
          "/public/carousel"
        );
        this.carouselSlides = response.slides;
        return response;
      } catch (error: unknown) {
        console.error("Fetch carousel slides error:", error);
        throw error;
      }
    },

    async fetchBenefitCards(): Promise<{ benefits: BenefitCard[] }> {
      try {
        const api = useApi();
        const response = await api.get<{ benefits: BenefitCard[] }>(
          "/public/benefits"
        );
        this.benefitCards = response.benefits;
        return response;
      } catch (error: unknown) {
        console.error("Fetch benefit cards error:", error);
        throw error;
      }
    },

    async fetchLimitedTimeOffer(): Promise<{ offer: LimitedTimeOffer }> {
      try {
        const api = useApi();
        const response = await api.get<{ offer: LimitedTimeOffer }>(
          "/public/limited-time-offer"
        );
        this.limitedTimeOffer = response.offer;
        return response;
      } catch (error: unknown) {
        console.error("Fetch limited time offer error:", error);
        throw error;
      }
    },

    async fetchCompanySettings(): Promise<{ settings: CompanySettings }> {
      try {
        const api = useApi();
        const response = await api.get<{ settings: CompanySettings }>(
          "/public/company-settings"
        );
        this.companySettings = response.settings;
        return response;
      } catch (error: unknown) {
        console.error("Fetch company settings error:", error);

        // Graceful degradation for SSR
        if (import.meta.server) {
          console.warn("SSR: Using default company settings due to API error");
          const defaultSettings: CompanySettings = {
            id: "default",
            companyName: "Multi Store",
            contactEmail: "<EMAIL>",
            contactPhone: "",
            address: "",
            logoUrl: undefined,
            description: "Multi Store - Your one-stop shop for everything",
          };
          this.companySettings = defaultSettings;
          return { settings: defaultSettings };
        }

        throw error;
      }
    },

    async fetchLegalPage(slug: string): Promise<LegalPage> {
      try {
        const api = useApi();
        const response = await api.get<{ page: LegalPage }>(
          `/public/legal/${slug}`
        );
        this.legalPages[slug] = response.page;
        return response.page;
      } catch (error: unknown) {
        console.error("Fetch legal page error:", error);
        throw error;
      }
    },

    async subscribeToNewsletter(email: string): Promise<any> {
      try {
        const api = useApi();
        const response = await api.post(
          "/public/newsletter",
          { email },
          {
            showSuccess: true,
            successMessage: "Successfully subscribed to newsletter!",
          }
        );
        return response;
      } catch (error: unknown) {
        console.error("Newsletter subscription error:", error);
        throw error;
      }
    },

    // Admin methods for company settings
    async updateCompanySettings(
      settings: Partial<CompanySettings>
    ): Promise<{ settings: CompanySettings }> {
      try {
        const api = useApi();
        const response = await api.put<{ settings: CompanySettings }>(
          "/admin/content/company-settings",
          settings,
          {
            showSuccess: true,
            successMessage: "Company settings updated successfully!",
          }
        );

        // Update local state
        this.companySettings = response.settings;
        return response;
      } catch (error: unknown) {
        console.error("Update company settings error:", error);
        throw error;
      }
    },

    async uploadLogo(file: File): Promise<{ settings?: CompanySettings }> {
      try {
        const api = useApi();
        const formData = new FormData();
        formData.append("logo", file);

        const response = await api.upload<{ settings?: CompanySettings }>(
          "/admin/content/upload-logo",
          formData,
          {
            showLoading: true,
            showSuccess: true,
            loadingMessage: "Uploading logo...",
            successMessage: "Logo uploaded successfully!",
          }
        );

        // Update local state
        if (response.settings) {
          this.companySettings = response.settings;
        }

        return response;
      } catch (error: unknown) {
        console.error("Upload logo error:", error);
        throw error;
      }
    },

    async removeLogo(): Promise<{ settings?: CompanySettings }> {
      try {
        const api = useApi();
        const response = await api.delete<{ settings?: CompanySettings }>(
          "/admin/content/remove-logo",
          {
            showSuccess: true,
            successMessage: "Logo removed successfully!",
          }
        );

        // Update local state
        if (response.settings) {
          this.companySettings = response.settings;
        }

        return response;
      } catch (error: unknown) {
        console.error("Remove logo error:", error);
        throw error;
      }
    },

    // Initialize homepage content
    async initializeHomepageContent(): Promise<void> {
      this.loading = true;
      try {
        await Promise.all([
          this.fetchCarouselSlides(),
          this.fetchBenefitCards(),
          this.fetchLimitedTimeOffer(),
          this.fetchCompanySettings(),
        ]);
      } catch (error: unknown) {
        console.error("Initialize homepage content error:", error);
      } finally {
        this.loading = false;
      }
    },

    // Get legal page with caching
    async getLegalPage(slug: string): Promise<LegalPage> {
      if (this.legalPages[slug]) {
        return this.legalPages[slug];
      }
      return await this.fetchLegalPage(slug);
    },

    // Homepage Design Methods
    async fetchHomepageDesign(): Promise<{ design: HomepageDesign }> {
      try {
        const api = useApi();
        const response = await api.get<{ design: HomepageDesign }>(
          "/public/homepage-design"
        );
        this.homepageDesign = response.design || {};
        return response;
      } catch (error: unknown) {
        console.error("Fetch homepage design error:", error);
        throw error;
      }
    },

    async updateHomepageDesign(
      design: HomepageDesign
    ): Promise<{ settings: HomepageDesign }> {
      try {
        const api = useApi();
        const response = await api.put<{ settings: HomepageDesign }>(
          "/admin/content/homepage-design",
          design,
          {
            showSuccess: true,
            successMessage: "Homepage design updated successfully!",
          }
        );

        // Update local state
        this.homepageDesign = response.settings;
        return response;
      } catch (error: unknown) {
        console.error("Update homepage design error:", error);
        throw error;
      }
    },
  },

  persist: {
    pick: ["companySettings"],
  },
});
