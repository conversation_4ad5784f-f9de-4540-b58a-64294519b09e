<template>
  <div class="space-y-6">
    <!-- Image Size Controls -->
    <div class="form-control">
      <label class="label">
        <span class="label-text font-medium">Image Size</span>
        <button
          type="button"
          @click="toggleAspectRatio"
          :class="[
            'btn btn-xs',
            localImageStyles.maintainAspectRatio
              ? 'btn-primary'
              : 'btn-outline',
          ]"
        >
          <Icon
            :name="
              localImageStyles.maintainAspectRatio
                ? 'heroicons:lock-closed'
                : 'heroicons:lock-open'
            "
            class="h-3 w-3"
          />
          <span class="ml-1">{{
            localImageStyles.maintainAspectRatio ? "Locked" : "Unlocked"
          }}</span>
        </button>
      </label>
      <div class="grid grid-cols-2 gap-2">
        <div class="form-control">
          <label class="label py-1">
            <span class="label-text-alt">Width (px)</span>
          </label>
          <input
            v-model.number="localImageStyles.width"
            type="number"
            min="16"
            max="200"
            class="input input-bordered input-sm"
            @input="updateImageSize('width', $event.target.value)"
          />
        </div>
        <div class="form-control">
          <label class="label py-1">
            <span class="label-text-alt">Height (px)</span>
          </label>
          <input
            v-model.number="localImageStyles.height"
            type="number"
            min="16"
            max="200"
            class="input input-bordered input-sm"
            @input="updateImageSize('height', $event.target.value)"
            :disabled="localImageStyles.maintainAspectRatio"
          />
        </div>
      </div>
    </div>

    <!-- Image Margin Controls -->
    <div class="form-control">
      <label class="label">
        <span class="label-text font-medium">Image Margin</span>
        <button
          type="button"
          @click="toggleImageMarginLink"
          :class="[
            'btn btn-xs',
            imageMarginLinked ? 'btn-primary' : 'btn-outline',
          ]"
        >
          <Icon
            :name="
              imageMarginLinked ? 'heroicons:link' : 'heroicons:link-slash'
            "
            class="h-3 w-3"
          />
        </button>
      </label>
      <div class="grid grid-cols-2 gap-2">
        <div class="form-control">
          <label class="label py-1">
            <span class="label-text-alt">Top</span>
          </label>
          <input
            v-model.number="localImageStyles.marginTop"
            type="number"
            min="0"
            max="100"
            class="input input-bordered input-sm"
            @input="updateImageMargin('top', $event.target.value)"
          />
        </div>
        <div class="form-control">
          <label class="label py-1">
            <span class="label-text-alt">Right</span>
          </label>
          <input
            v-model.number="localImageStyles.marginRight"
            type="number"
            min="0"
            max="100"
            class="input input-bordered input-sm"
            @input="updateImageMargin('right', $event.target.value)"
          />
        </div>
        <div class="form-control">
          <label class="label py-1">
            <span class="label-text-alt">Bottom</span>
          </label>
          <input
            v-model.number="localImageStyles.marginBottom"
            type="number"
            min="0"
            max="100"
            class="input input-bordered input-sm"
            @input="updateImageMargin('bottom', $event.target.value)"
          />
        </div>
        <div class="form-control">
          <label class="label py-1">
            <span class="label-text-alt">Left</span>
          </label>
          <input
            v-model.number="localImageStyles.marginLeft"
            type="number"
            min="0"
            max="100"
            class="input input-bordered input-sm"
            @input="updateImageMargin('left', $event.target.value)"
          />
        </div>
      </div>
    </div>

    <!-- Image Padding Controls -->
    <div class="form-control">
      <label class="label">
        <span class="label-text font-medium">Image Padding</span>
        <button
          type="button"
          @click="toggleImagePaddingLink"
          :class="[
            'btn btn-xs',
            imagePaddingLinked ? 'btn-primary' : 'btn-outline',
          ]"
        >
          <Icon
            :name="
              imagePaddingLinked ? 'heroicons:link' : 'heroicons:link-slash'
            "
            class="h-3 w-3"
          />
        </button>
      </label>
      <div class="grid grid-cols-2 gap-2">
        <div class="form-control">
          <label class="label py-1">
            <span class="label-text-alt">Top</span>
          </label>
          <input
            v-model.number="localImageStyles.paddingTop"
            type="number"
            min="0"
            max="100"
            class="input input-bordered input-sm"
            @input="updateImagePadding('top', $event.target.value)"
          />
        </div>
        <div class="form-control">
          <label class="label py-1">
            <span class="label-text-alt">Right</span>
          </label>
          <input
            v-model.number="localImageStyles.paddingRight"
            type="number"
            min="0"
            max="100"
            class="input input-bordered input-sm"
            @input="updateImagePadding('right', $event.target.value)"
          />
        </div>
        <div class="form-control">
          <label class="label py-1">
            <span class="label-text-alt">Bottom</span>
          </label>
          <input
            v-model.number="localImageStyles.paddingBottom"
            type="number"
            min="0"
            max="100"
            class="input input-bordered input-sm"
            @input="updateImagePadding('bottom', $event.target.value)"
          />
        </div>
        <div class="form-control">
          <label class="label py-1">
            <span class="label-text-alt">Left</span>
          </label>
          <input
            v-model.number="localImageStyles.paddingLeft"
            type="number"
            min="0"
            max="100"
            class="input input-bordered input-sm"
            @input="updateImagePadding('left', $event.target.value)"
          />
        </div>
      </div>
    </div>

    <!-- Preview -->
    <div class="form-control">
      <label class="label">
        <span class="label-text font-medium">Preview</span>
      </label>
      <div
        class="p-4 bg-base-200 rounded-lg border-2 border-dashed border-base-300"
      >
        <div class="flex items-center gap-2">
          <div
            :style="previewImageStyle"
            class="bg-primary rounded flex items-center justify-center flex-shrink-0"
          >
            <span class="text-primary-content text-xs">Logo</span>
          </div>
          <span class="text-base-content">Company Name</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  imageStyles: {
    type: Object,
    default: () => ({
      width: 32,
      height: 32,
      maintainAspectRatio: true,
      marginTop: 0,
      marginRight: 0,
      marginBottom: 0,
      marginLeft: 0,
      paddingTop: 0,
      paddingRight: 0,
      paddingBottom: 0,
      paddingLeft: 0,
    }),
  },
});

// Emits
const emit = defineEmits(["update:imageStyles"]);

// Local state
const localImageStyles = ref({ ...props.imageStyles });
const imageMarginLinked = ref(false);
const imagePaddingLinked = ref(false);

// Computed preview style
const previewImageStyle = computed(() => ({
  width: `${localImageStyles.value.width}px`,
  height: localImageStyles.value.maintainAspectRatio
    ? `${localImageStyles.value.width}px`
    : `${localImageStyles.value.height}px`,
  marginTop: `${localImageStyles.value.marginTop}px`,
  marginRight: `${localImageStyles.value.marginRight}px`,
  marginBottom: `${localImageStyles.value.marginBottom}px`,
  marginLeft: `${localImageStyles.value.marginLeft}px`,
  paddingTop: `${localImageStyles.value.paddingTop}px`,
  paddingRight: `${localImageStyles.value.paddingRight}px`,
  paddingBottom: `${localImageStyles.value.paddingBottom}px`,
  paddingLeft: `${localImageStyles.value.paddingLeft}px`,
}));

// Methods
const updateImageStyles = () => {
  emit("update:imageStyles", { ...localImageStyles.value });
};

// Image styling methods
const toggleAspectRatio = () => {
  localImageStyles.value.maintainAspectRatio =
    !localImageStyles.value.maintainAspectRatio;
  if (localImageStyles.value.maintainAspectRatio) {
    localImageStyles.value.height = localImageStyles.value.width;
  }
  updateImageStyles();
};

const updateImageSize = (dimension, value) => {
  const numValue = parseInt(value) || 16;

  if (dimension === "width") {
    localImageStyles.value.width = numValue;
    if (localImageStyles.value.maintainAspectRatio) {
      localImageStyles.value.height = numValue;
    }
  } else {
    if (!localImageStyles.value.maintainAspectRatio) {
      localImageStyles.value.height = numValue;
    }
  }

  updateImageStyles();
};

const toggleImageMarginLink = () => {
  imageMarginLinked.value = !imageMarginLinked.value;
};

const toggleImagePaddingLink = () => {
  imagePaddingLinked.value = !imagePaddingLinked.value;
};

const updateImageMargin = (direction, value) => {
  const numValue = parseInt(value) || 0;

  if (imageMarginLinked.value) {
    localImageStyles.value.marginTop = numValue;
    localImageStyles.value.marginRight = numValue;
    localImageStyles.value.marginBottom = numValue;
    localImageStyles.value.marginLeft = numValue;
  } else {
    localImageStyles.value[
      `margin${direction.charAt(0).toUpperCase() + direction.slice(1)}`
    ] = numValue;
  }

  updateImageStyles();
};

const updateImagePadding = (direction, value) => {
  const numValue = parseInt(value) || 0;

  if (imagePaddingLinked.value) {
    localImageStyles.value.paddingTop = numValue;
    localImageStyles.value.paddingRight = numValue;
    localImageStyles.value.paddingBottom = numValue;
    localImageStyles.value.paddingLeft = numValue;
  } else {
    localImageStyles.value[
      `padding${direction.charAt(0).toUpperCase() + direction.slice(1)}`
    ] = numValue;
  }

  updateImageStyles();
};

// Watch for prop changes
watch(
  () => props.imageStyles,
  (newImageStyles) => {
    localImageStyles.value = { ...newImageStyles };
  },
  { deep: true }
);
</script>
