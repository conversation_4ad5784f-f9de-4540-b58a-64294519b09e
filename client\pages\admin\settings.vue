<template>
  <div>
    <!-- <PERSON> Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold mb-2">Company Settings</h1>
      <p class="text-base-content/70">
        Manage your store's company information, logo, and branding
      </p>
    </div>

    <!-- Settings Form -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Settings -->
      <div class="lg:col-span-2">
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <h2 class="card-title mb-6">Company Information</h2>

            <form @submit.prevent="handleSubmit" class="space-y-6">
              <!-- Company Name -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Company Name *</span>
                </label>
                <input
                  v-model="form.companyName"
                  type="text"
                  placeholder="Enter company name"
                  class="input input-bordered w-full"
                  required
                />
              </div>

              <!-- Contact Information -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">Email *</span>
                  </label>
                  <input
                    v-model="form.companyEmail"
                    type="email"
                    placeholder="<EMAIL>"
                    class="input input-bordered w-full"
                    required
                  />
                </div>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">Phone</span>
                  </label>
                  <input
                    v-model="form.companyPhone"
                    type="tel"
                    placeholder="+****************"
                    class="input input-bordered w-full"
                  />
                </div>
              </div>

              <!-- Address -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Address Line 1</span>
                </label>
                <input
                  v-model="form.companyAddress1"
                  type="text"
                  placeholder="123 Business Street"
                  class="input input-bordered w-full"
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Address Line 2</span>
                </label>
                <input
                  v-model="form.companyAddress2"
                  type="text"
                  placeholder="Suite 100"
                  class="input input-bordered w-full"
                />
              </div>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">City</span>
                  </label>
                  <input
                    v-model="form.companyCity"
                    type="text"
                    placeholder="Business City"
                    class="input input-bordered w-full"
                  />
                </div>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">State/Province</span>
                  </label>
                  <input
                    v-model="form.companyState"
                    type="text"
                    placeholder="BC"
                    class="input input-bordered w-full"
                  />
                </div>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">ZIP/Postal Code</span>
                  </label>
                  <input
                    v-model="form.companyZip"
                    type="text"
                    placeholder="12345"
                    class="input input-bordered w-full"
                  />
                </div>
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Country</span>
                </label>
                <input
                  v-model="form.companyCountry"
                  type="text"
                  placeholder="United States"
                  class="input input-bordered w-full"
                />
              </div>

              <!-- Social Media -->
              <div class="divider">Social Media</div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">Facebook URL</span>
                  </label>
                  <input
                    v-model="form.facebookUrl"
                    type="url"
                    placeholder="https://facebook.com/yourstore"
                    class="input input-bordered w-full"
                  />
                </div>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">Twitter URL</span>
                  </label>
                  <input
                    v-model="form.twitterUrl"
                    type="url"
                    placeholder="https://twitter.com/yourstore"
                    class="input input-bordered w-full"
                  />
                </div>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">Instagram URL</span>
                  </label>
                  <input
                    v-model="form.instagramUrl"
                    type="url"
                    placeholder="https://instagram.com/yourstore"
                    class="input input-bordered w-full"
                  />
                </div>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">LinkedIn URL</span>
                  </label>
                  <input
                    v-model="form.linkedinUrl"
                    type="url"
                    placeholder="https://linkedin.com/company/yourstore"
                    class="input input-bordered w-full"
                  />
                </div>
              </div>

              <!-- SEO Settings -->
              <div class="divider">SEO Settings</div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Meta Title</span>
                </label>
                <input
                  v-model="form.metaTitle"
                  type="text"
                  placeholder="Your Store - Best Products Online"
                  class="input input-bordered w-full"
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Meta Description</span>
                </label>
                <textarea
                  v-model="form.metaDescription"
                  placeholder="Discover amazing products at great prices..."
                  class="textarea textarea-bordered h-24 w-full"
                ></textarea>
              </div>

              <!-- Newsletter Settings -->
              <div class="divider">Newsletter Settings</div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Newsletter Title</span>
                </label>
                <input
                  v-model="form.newsletterTitle"
                  type="text"
                  placeholder="Subscribe to our newsletter"
                  class="input input-bordered w-full"
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium"
                    >Newsletter Description</span
                  >
                </label>
                <textarea
                  v-model="form.newsletterDescription"
                  placeholder="Get the latest updates on new products and exclusive offers."
                  class="textarea textarea-bordered h-20 w-full"
                ></textarea>
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Copyright Text</span>
                </label>
                <input
                  v-model="form.copyrightText"
                  type="text"
                  placeholder="All rights reserved"
                  class="input input-bordered w-full"
                />
              </div>

              <!-- Submit Button -->
              <div class="form-control pt-4">
                <button
                  type="submit"
                  class="btn btn-primary"
                  :class="{ loading: loading }"
                  :disabled="loading"
                >
                  <span v-if="!loading">Save Settings</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Logo Upload Section -->
      <div class="lg:col-span-1">
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <h2 class="card-title mb-6">Store Logo</h2>

            <!-- Current Logo Display -->
            <div class="mb-6">
              <div
                class="w-full h-32 bg-base-200 rounded-lg flex items-center justify-center border-2 border-dashed border-base-300"
              >
                <div v-if="currentLogo" class="text-center">
                  <NuxtImg
                    :src="currentLogo"
                    alt="Store Logo"
                    class="max-h-24 max-w-full object-contain mx-auto"
                    preset="logo"
                  />
                </div>
                <div v-else class="text-center text-base-content/50">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-8 w-8 mx-auto mb-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                  <p class="text-sm">No logo uploaded</p>
                </div>
              </div>
            </div>

            <!-- Logo Upload -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Upload New Logo</span>
              </label>
              <input
                type="file"
                accept="image/*"
                @change="handleLogoUpload"
                class="file-input file-input-bordered w-full"
              />
              <label class="label">
                <span class="label-text-alt"
                  >Recommended: PNG or SVG, max 2MB</span
                >
              </label>
            </div>

            <!-- Logo Actions -->
            <div class="flex gap-2 mt-4">
              <button
                v-if="currentLogo"
                @click="removeLogo"
                class="btn btn-outline btn-error btn-sm flex-1"
              >
                Remove Logo
              </button>
            </div>
          </div>
        </div>

        <!-- Preview Section -->
        <div class="card bg-base-100 shadow-xl mt-6">
          <div class="card-body">
            <h3 class="card-title text-lg mb-4">Preview</h3>
            <div class="space-y-4">
              <div>
                <p class="text-sm font-medium mb-2">Navbar Preview:</p>
                <div class="bg-base-200 p-3 rounded-lg">
                  <div class="flex items-center gap-2">
                    <div v-if="currentLogo" class="w-8 h-8">
                      <NuxtImg
                        :src="currentLogo"
                        alt="Logo"
                        class="w-full h-full object-contain"
                        preset="logo"
                      />
                    </div>
                    <svg
                      v-else
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                      />
                    </svg>
                    <span class="font-bold">{{
                      form.companyName || "Multi Store"
                    }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Logo Image Styling Section -->
        <div v-if="currentLogo" class="card bg-base-100 shadow-xl mt-6">
          <div class="card-body">
            <h2 class="card-title mb-6">Logo Image Styling</h2>
            <LogoImageStyleEditor v-model:imageStyles="form.logoImageStyles" />
          </div>
        </div>

        <!-- Logo Text Styling Section -->
        <div class="card bg-base-100 shadow-xl mt-6">
          <div class="card-body">
            <h2 class="card-title mb-6">Logo Text Styling</h2>
            <TextStyleEditor
              v-model:text="form.logoText"
              v-model:styles="form.logoTextStyles"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Success/Error Messages -->
    <div v-if="message" class="toast toast-top toast-end">
      <div
        class="alert"
        :class="messageType === 'success' ? 'alert-success' : 'alert-error'"
      >
        <span>{{ message }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
// Use admin layout
definePageMeta({
  middleware: "admin",
  layout: "admin",
});

// Meta tags
useHead({
  title: "Company Settings - Admin",
  meta: [
    { name: "description", content: "Manage company settings and branding" },
  ],
});

const contentStore = useContentStore();

// Form state
const form = reactive({
  companyName: "",
  companyEmail: "",
  companyPhone: "",
  companyAddress1: "",
  companyAddress2: "",
  companyCity: "",
  companyState: "",
  companyZip: "",
  companyCountry: "",
  facebookUrl: "",
  twitterUrl: "",
  instagramUrl: "",
  linkedinUrl: "",
  metaTitle: "",
  metaDescription: "",
  newsletterTitle: "",
  newsletterDescription: "",
  copyrightText: "",
  logoText: "",
  logoImageStyles: {
    width: 32,
    height: 32,
    maintainAspectRatio: true,
    marginTop: 0,
    marginRight: 0,
    marginBottom: 0,
    marginLeft: 0,
    paddingTop: 0,
    paddingRight: 0,
    paddingBottom: 0,
    paddingLeft: 0,
  },
  logoTextStyles: {
    fontFamily: "Inter",
    fontSize: 20,
    fontWeight: "600",
    fontStyle: "normal",
    color: "#000000",
    marginTop: 0,
    marginRight: 0,
    marginBottom: 0,
    marginLeft: 0,
    paddingTop: 0,
    paddingRight: 0,
    paddingBottom: 0,
    paddingLeft: 0,
    verticalAlign: "center",
  },
});

const loading = ref(false);
const message = ref("");
const messageType = ref("success");

// Make currentLogo reactive to store changes
const currentLogo = computed(() => contentStore.companySettings?.logoUrl || "");

// Load current settings
onMounted(async () => {
  try {
    await contentStore.fetchCompanySettings();
    const settings = contentStore.companySettings;

    if (settings) {
      Object.keys(form).forEach((key) => {
        if (settings[key] !== undefined) {
          form[key] = settings[key] || "";
        }
      });

      // Logo is now handled by computed property
    }
  } catch (error) {
    console.error("Error loading settings:", error);
  }
});

// Handle form submission
const handleSubmit = async () => {
  loading.value = true;
  message.value = "";

  try {
    await contentStore.updateCompanySettings(form);

    message.value = "Settings saved successfully!";
    messageType.value = "success";

    // Clear message after 3 seconds
    setTimeout(() => {
      message.value = "";
    }, 3000);
  } catch (error) {
    message.value = "Failed to save settings. Please try again.";
    messageType.value = "error";

    setTimeout(() => {
      message.value = "";
    }, 5000);
  } finally {
    loading.value = false;
  }
};

// Handle logo upload
const handleLogoUpload = async (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // Validate file
  if (file.size > 2 * 1024 * 1024) {
    message.value = "File size must be less than 2MB";
    messageType.value = "error";
    setTimeout(() => {
      message.value = "";
    }, 3000);
    return;
  }

  if (!file.type.startsWith("image/")) {
    message.value = "Please select an image file";
    messageType.value = "error";
    setTimeout(() => {
      message.value = "";
    }, 3000);
    return;
  }

  try {
    loading.value = true;
    message.value = "";

    // Upload to server
    await contentStore.uploadLogo(file);

    // Logo display will update automatically via computed property

    message.value = "Logo uploaded successfully!";
    messageType.value = "success";

    setTimeout(() => {
      message.value = "";
    }, 3000);
  } catch (error) {
    message.value = "Failed to upload logo. Please try again.";
    messageType.value = "error";

    setTimeout(() => {
      message.value = "";
    }, 5000);
  } finally {
    loading.value = false;
  }
};

// Remove logo
const removeLogo = async () => {
  try {
    loading.value = true;
    message.value = "";

    await contentStore.removeLogo();

    currentLogo.value = "";

    message.value = "Logo removed successfully!";
    messageType.value = "success";

    setTimeout(() => {
      message.value = "";
    }, 3000);
  } catch (error) {
    message.value = "Failed to remove logo. Please try again.";
    messageType.value = "error";

    setTimeout(() => {
      message.value = "";
    }, 5000);
  } finally {
    loading.value = false;
  }
};
</script>
