version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: multistore-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: multistore_dev
      POSTGRES_USER: multistore_user
      POSTGRES_PASSWORD: multistore_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - multistore-dev-network

  # Backend API Server (Development)
  api:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: multistore-api-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_URL: "**************************************************************/multistore_dev?schema=public"
      JWT_SECRET: "development-jwt-secret-key"
      JWT_EXPIRES_IN: "7d"
      PORT: 3001
      BASE_URL: "http://localhost:3001"
      CORS_ORIGIN: "http://localhost:3000"
    volumes:
      - ./server:/app
      - uploads_dev_data:/app/uploads
      - /app/node_modules
    ports:
      - "3001:3001"
    depends_on:
      - postgres
    networks:
      - multistore-dev-network
    command: ["pnpm", "run", "dev"]

  # Frontend Client (Development)
  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: multistore-client-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      NUXT_PUBLIC_API_BASE: "http://localhost:3001/api"
    volumes:
      - ./client:/app
      - /app/node_modules
      - /app/.nuxt
    ports:
      - "3000:3000"
    depends_on:
      - api
    networks:
      - multistore-dev-network
    command: ["pnpm", "run", "dev"]

volumes:
  postgres_dev_data:
    driver: local
  uploads_dev_data:
    driver: local

networks:
  multistore-dev-network:
    driver: bridge
