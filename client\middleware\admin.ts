import type { RouteLocationNormalized } from "vue-router";

export default defineNuxtRouteMiddleware(
  (_to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
    const authStore = useAuthStore();

    // Check if user is authenticated
    if (!authStore.isAuthenticated) {
      return navigateTo("/login");
    }

    // Check if user has admin privileges
    if (
      !(
        authStore.user?.role === "ADMIN" ||
        authStore.user?.role === "SUPERADMIN"
      )
    ) {
      throw createError({
        statusCode: 403,
        statusMessage: "Access denied. Admin privileges required.",
      });
    }
  }
);
