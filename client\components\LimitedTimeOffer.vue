<template>
  <div v-if="displayOffer" class="max-w-4xl mx-auto text-center px-4">
    <!-- Badge -->
    <div class="badge badge-accent badge-lg mb-4">
      {{ displayOffer.badge }}
    </div>
    
    <!-- Title -->
    <h2 class="text-4xl md:text-6xl font-bold mb-4">
      {{ displayOffer.title }}
    </h2>
    
    <!-- Subtitle -->
    <p class="text-xl md:text-2xl font-light mb-8 uppercase tracking-wide">
      {{ displayOffer.subtitle }}
    </p>
    
    <!-- Countdown Timer -->
    <div v-if="displayOffer.endDate" class="mb-8">
      <div class="grid grid-flow-col gap-5 text-center auto-cols-max">
        <div class="flex flex-col p-2 bg-neutral rounded-box text-neutral-content">
          <span class="countdown font-mono text-5xl">
            <span :style="`--value:${timeLeft.days};`"></span>
          </span>
          days
        </div>
        <div class="flex flex-col p-2 bg-neutral rounded-box text-neutral-content">
          <span class="countdown font-mono text-5xl">
            <span :style="`--value:${timeLeft.hours};`"></span>
          </span>
          hours
        </div>
        <div class="flex flex-col p-2 bg-neutral rounded-box text-neutral-content">
          <span class="countdown font-mono text-5xl">
            <span :style="`--value:${timeLeft.minutes};`"></span>
          </span>
          min
        </div>
        <div class="flex flex-col p-2 bg-neutral rounded-box text-neutral-content">
          <span class="countdown font-mono text-5xl">
            <span :style="`--value:${timeLeft.seconds};`"></span>
          </span>
          sec
        </div>
      </div>
    </div>
    
    <!-- CTA Button -->
    <NuxtLink 
      :to="displayOffer.buttonLink || '#trending'" 
      class="btn btn-accent btn-lg"
    >
      {{ displayOffer.buttonText }}
    </NuxtLink>
  </div>
</template>

<script setup>
const contentStore = useContentStore()

// Get limited time offer from store
const storeOffer = computed(() => contentStore.limitedTimeOffer)

// Default offer if none is loaded
const defaultOffer = {
  id: 'default-1',
  badge: 'Limited Time Offer',
  title: 'Summer Sale',
  subtitle: 'Hurry Up',
  buttonText: 'Shop Now',
  buttonLink: '#trending',
  endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
  isActive: true
}

// Use default offer if no offer is available or if offer is not active
const displayOffer = computed(() => {
  if (storeOffer.value && contentStore.isLimitedTimeOfferActive) {
    return storeOffer.value
  }
  return defaultOffer
})

// Countdown timer
const timeLeft = ref({
  days: 0,
  hours: 0,
  minutes: 0,
  seconds: 0
})

const updateCountdown = () => {
  if (!displayOffer.value?.endDate) return
  
  const now = new Date().getTime()
  const endTime = new Date(displayOffer.value.endDate).getTime()
  const distance = endTime - now
  
  if (distance > 0) {
    timeLeft.value = {
      days: Math.floor(distance / (1000 * 60 * 60 * 24)),
      hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
      minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
      seconds: Math.floor((distance % (1000 * 60)) / 1000)
    }
  } else {
    timeLeft.value = { days: 0, hours: 0, minutes: 0, seconds: 0 }
  }
}

// Update countdown every second
let countdownInterval = null

onMounted(() => {
  updateCountdown()
  countdownInterval = setInterval(updateCountdown, 1000)
})

onUnmounted(() => {
  if (countdownInterval) {
    clearInterval(countdownInterval)
  }
})
</script>
