import express from "express";
import {
  getCarouselSlides,
  getBenefitCards,
  getPublicCategories,
  getTrendingProducts,
  getNewArrivals,
  getProductBySlug,
  getCategoryProducts,
  getLimitedTimeOffer,
  subscribeToNewsletter,
  getLegalPage,
  getCompanySettings,
} from "../controllers/public.controller";
import { wrapController } from "../utils/wrapController";

const router: express.Router = express.Router();

// Content routes
router.get("/carousel", wrapController(getCarouselSlides));
router.get("/benefits", wrapController(getBenefitCards));
router.get("/categories", wrapController(getPublicCategories));

// Product routes
router.get("/products/trending", wrapController(getTrendingProducts));
router.get("/products/new-arrivals", wrapController(getNewArrivals));
router.get("/products/:slug", wrapController(getProductBySlug));

// Category routes
router.get("/categories/:slug/products", wrapController(getCategoryProducts));

// Other routes
router.get("/limited-time-offer", wrapController(getLimitedTimeOffer));
router.post("/newsletter", wrapController(subscribeToNewsletter));
router.get("/legal/:slug", wrapController(getLegalPage));
router.get("/company-settings", wrapController(getCompanySettings));

export default router;
