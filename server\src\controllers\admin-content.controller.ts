import { Request, Response, NextFunction } from "express";
import { PrismaClient } from "@prisma/client";
import { createError } from "../utils/wrapController";
import { deleteFile, getFileUrl } from "../middleware/upload";

const prisma = new PrismaClient();

// Request interfaces
export interface CreateCarouselSlideRequest {
  title: string;
  subtitle?: string;
  image?: string;
  link?: string;
  buttonText?: string;
  isActive: boolean;
  sortOrder: number;
}

export interface UpdateCompanySettingsRequest {
  companyName?: string;
  companyEmail?: string;
  companyPhone?: string;
  companyAddress1?: string;
  companyAddress2?: string;
  companyCity?: string;
  companyState?: string;
  companyZip?: string;
  companyCountry?: string;
  metaTitle?: string;
  metaDescription?: string;
  copyrightText?: string;
  newsletterTitle?: string;
  newsletterDescription?: string;
}

export interface HomepageDesignRequest {
  navbarDesign?: any;
  heroDesign?: any;
  benefitCardsDesign?: any;
  sectionBackgrounds?: any;
  cardsDesign?: any;
  typographyDesign?: any;
  pageDesign?: any;
  buttonDesign?: any;
}

/**
 * GET /api/admin/content/carousel
 * Get all carousel slides
 */
export const getCarouselSlides = async (
  _req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const slides = await prisma.carouselSlide.findMany({
      orderBy: { sortOrder: "asc" },
    });

    res.json({ slides });
  } catch (error) {
    console.error("Get carousel slides error:", error);
    next(createError(500, "Failed to get carousel slides"));
  }
};

/**
 * POST /api/admin/content/carousel
 * Create new carousel slide
 */
export const createCarouselSlide = async (
  req: Request<{}, {}, CreateCarouselSlideRequest>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { title, subtitle, image, link, buttonText, isActive, sortOrder } =
      req.body;

    const slide = await prisma.carouselSlide.create({
      data: {
        title,
        subtitle,
        image,
        link,
        buttonText,
        isActive,
        sortOrder,
      },
    });

    res.status(201).json({
      message: "Carousel slide created successfully",
      slide,
    });
  } catch (error) {
    console.error("Create carousel slide error:", error);
    next(createError(500, "Failed to create carousel slide"));
  }
};

/**
 * GET /api/admin/content/company-settings
 * Get company settings
 */
export const getCompanySettings = async (
  _req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const settings = await prisma.companySettings.findFirst();

    // Convert relative logoUrl to full URL
    if (settings && settings.logoUrl) {
      settings.logoUrl = getFileUrl(settings.logoUrl);
    }

    res.json({ settings });
  } catch (error) {
    console.error("Get company settings error:", error);
    next(createError(500, "Failed to get company settings"));
  }
};

/**
 * PUT /api/admin/content/company-settings
 * Update company settings
 */
export const updateCompanySettings = async (
  req: Request<{}, {}, UpdateCompanySettingsRequest>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const updateData = req.body;

    // Get existing settings or create new ones
    let settings = await prisma.companySettings.findFirst();

    if (settings) {
      settings = await prisma.companySettings.update({
        where: { id: settings.id },
        data: updateData,
      });
    } else {
      settings = await prisma.companySettings.create({
        data: {
          id: "default",
          companyName: updateData.companyName || "Multi Store",
          companyEmail: updateData.companyEmail || "<EMAIL>",
          ...updateData,
        },
      });
    }

    // Convert relative logoUrl to full URL
    if (settings.logoUrl) {
      settings.logoUrl = getFileUrl(settings.logoUrl);
    }

    res.json({
      message: "Company settings updated successfully",
      settings,
    });
  } catch (error) {
    console.error("Update company settings error:", error);
    next(createError(500, "Failed to update company settings"));
  }
};

/**
 * POST /api/admin/content/upload-logo
 * Upload company logo
 */
export const uploadLogo = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.file) {
      next(createError(400, "No file uploaded"));
      return;
    }

    const logoPath = `logos/${req.file.filename}`;

    // Get existing settings
    let settings = await prisma.companySettings.findFirst();

    // Delete old logo if exists
    if (settings?.logoUrl) {
      deleteFile(settings.logoUrl);
    }

    // Update or create settings with new logo
    if (settings) {
      settings = await prisma.companySettings.update({
        where: { id: settings.id },
        data: { logoUrl: logoPath },
      });
    } else {
      settings = await prisma.companySettings.create({
        data: {
          id: "default",
          companyName: "Multi Store",
          companyEmail: "<EMAIL>",
          logoUrl: logoPath,
        },
      });
    }

    // Convert to full URL
    settings.logoUrl = getFileUrl(settings.logoUrl);

    res.json({
      message: "Logo uploaded successfully",
      settings,
    });
  } catch (error) {
    console.error("Upload logo error:", error);
    next(createError(500, "Failed to upload logo"));
  }
};

/**
 * DELETE /api/admin/content/remove-logo
 * Remove company logo
 */
export const removeLogo = async (
  _req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const settings = await prisma.companySettings.findFirst();

    if (!settings || !settings.logoUrl) {
      next(createError(404, "No logo found"));
      return;
    }

    // Delete file
    deleteFile(settings.logoUrl);

    // Update settings
    const updatedSettings = await prisma.companySettings.update({
      where: { id: settings.id },
      data: { logoUrl: null },
    });

    res.json({
      message: "Logo removed successfully",
      settings: updatedSettings,
    });
  } catch (error) {
    console.error("Remove logo error:", error);
    next(createError(500, "Failed to remove logo"));
  }
};

/**
 * GET /api/admin/content/homepage-design
 * Get homepage design settings
 */
export const getHomepageDesign = async (
  _req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const settings = await prisma.companySettings.findFirst({
      select: {
        navbarDesign: true,
        heroDesign: true,
        benefitCardsDesign: true,
        sectionBackgrounds: true,
        cardsDesign: true,
        typographyDesign: true,
        pageDesign: true,
        buttonDesign: true,
      },
    });

    if (!settings) {
      // Return default settings if none exist
      res.json({
        settings: {
          navbarDesign: getDefaultNavbarDesign(),
          heroDesign: getDefaultHeroDesign(),
          benefitCardsDesign: getDefaultBenefitCardsDesign(),
          sectionBackgrounds: getDefaultSectionBackgrounds(),
          cardsDesign: getDefaultCardsDesign(),
          typographyDesign: getDefaultTypographyDesign(),
          pageDesign: getDefaultPageDesign(),
          buttonDesign: getDefaultButtonDesign(),
        },
      });
      return;
    }

    // Process hero design slides to convert relative image URLs to full URLs
    if (settings.heroDesign && typeof settings.heroDesign === "object") {
      const heroDesign = settings.heroDesign as any;
      if (heroDesign.slides && Array.isArray(heroDesign.slides)) {
        heroDesign.slides = heroDesign.slides.map((slide: any) => {
          // Clean up invalid URLs but keep the slide content
          let cleanImage = slide.image;

          if (!slide.image) {
            cleanImage = null;
          } else if (slide.image.startsWith("blob:")) {
            cleanImage = null; // Remove blob URL but keep slide
          } else if (
            slide.image.startsWith("/") &&
            !slide.image.startsWith("/uploads/")
          ) {
            // Remove invalid paths that start with / but aren't proper upload paths
            cleanImage = null;
          } else if (
            slide.image.startsWith("carousel/") ||
            slide.image.startsWith("logos/")
          ) {
            // Convert relative paths to full URLs
            cleanImage = getFileUrl(cleanImage);
          } else if (slide.image.startsWith("http")) {
            // Already a full URL, keep as is
            cleanImage = slide.image;
          } else {
            // Unknown format, remove it
            cleanImage = null;
          }

          return {
            ...slide,
            image: cleanImage,
          };
        });
      }
    }

    // Process benefit cards design to convert relative image URLs to full URLs
    if (
      (settings as any).benefitCardsDesign &&
      typeof (settings as any).benefitCardsDesign === "object"
    ) {
      const benefitCardsDesign = (settings as any).benefitCardsDesign as any;
      if (benefitCardsDesign.cards && Array.isArray(benefitCardsDesign.cards)) {
        benefitCardsDesign.cards = benefitCardsDesign.cards.map((card: any) => {
          // Clean up invalid URLs but keep the card content
          let cleanImage = card.image;

          if (!card.image) {
            cleanImage = null;
          } else if (card.image.startsWith("blob:")) {
            cleanImage = null; // Remove blob URL but keep card
          } else if (
            card.image.startsWith("/") &&
            !card.image.startsWith("/uploads/")
          ) {
            // Remove invalid paths that start with / but aren't proper upload paths
            cleanImage = null;
          } else if (
            card.image.startsWith("benefits/") ||
            card.image.startsWith("carousel/") ||
            card.image.startsWith("logos/")
          ) {
            // Convert relative paths to full URLs
            cleanImage = getFileUrl(cleanImage);
          } else if (card.image.startsWith("http")) {
            // Already a full URL, keep as is
            cleanImage = card.image;
          } else {
            // Unknown format, remove it
            cleanImage = null;
          }

          return {
            ...card,
            image: cleanImage,
          };
        });
      }
    }

    res.json({ settings });
  } catch (error) {
    console.error("Fetch homepage design error:", error);
    next(createError(500, "Failed to fetch homepage design settings"));
  }
};

/**
 * PUT /api/admin/content/homepage-design
 * Update homepage design settings
 */
export const updateHomepageDesign = async (
  req: Request<{}, {}, HomepageDesignRequest>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const {
      navbarDesign,
      heroDesign,
      benefitCardsDesign,
      sectionBackgrounds,
      cardsDesign,
      typographyDesign,
      pageDesign,
      buttonDesign,
    } = req.body;

    // Get or create company settings
    let settings = await prisma.companySettings.findFirst();

    if (!settings) {
      settings = await prisma.companySettings.create({
        data: {
          id: "default",
          companyName: "Multi Store",
          companyEmail: "<EMAIL>",
          navbarDesign,
          heroDesign,
          benefitCardsDesign,
          sectionBackgrounds,
          cardsDesign,
          typographyDesign,
          pageDesign,
          buttonDesign,
        },
      });
    } else {
      settings = await prisma.companySettings.update({
        where: { id: settings.id },
        data: {
          navbarDesign,
          heroDesign,
          benefitCardsDesign,
          sectionBackgrounds,
          cardsDesign,
          typographyDesign,
          pageDesign,
          buttonDesign,
          updatedAt: new Date(),
        },
      });
    }

    res.json({
      success: true,
      message: "Homepage design settings updated successfully",
      settings,
    });
  } catch (error) {
    console.error("Update homepage design error:", error);
    next(createError(500, "Failed to update homepage design settings"));
  }
};

// Default design configurations
function getDefaultNavbarDesign() {
  return {
    width: "full",
    alignment: "center",
    height: "default",
    padding: { top: 0, right: 0, bottom: 0, left: 0 },
    margin: { top: 0, right: 0, bottom: 0, left: 0 },
    itemSpacing: "normal",
    linkStyle: "default",
    hoverStyle: "default",
    activeStyle: "default",
    textStyles: {
      fontFamily: "Inter",
      fontSize: 16,
      fontWeight: "500",
      color: "#000000",
    },
  };
}

function getDefaultHeroDesign() {
  return {
    width: "full",
    height: "500px",
    padding: { top: 0, right: 0, bottom: 0, left: 0 },
    margin: { top: 0, right: 0, bottom: 0, left: 0 },
    background: {
      type: "color",
      color: "#ffffff",
      gradient: { from: "#ffffff", to: "#f3f4f6", direction: "to-r" },
    },
    slides: [],
    imagePosition: "cover",
    textDesign: {
      title: {
        fontFamily: "Inter",
        fontSize: 48,
        fontWeight: "700",
        color: "#ffffff",
      },
      subtitle: {
        fontFamily: "Inter",
        fontSize: 18,
        fontWeight: "400",
        color: "#ffffff",
      },
    },
    buttonDesign: {
      style: "primary",
      size: "lg",
      rounded: "default",
      wide: false,
      block: false,
      square: false,
      dropShadow: false,
      backgroundColor: "#3b82f6",
      textColor: "#ffffff",
      borderColor: "#3b82f6",
    },
  };
}

function getDefaultBenefitCardsDesign() {
  return {
    backgroundColor: "#f8fafc",
    padding: {
      vertical: 60,
      horizontal: 20,
    },
    cards: [
      {
        id: "default-1",
        title: "Free Shipping",
        description: "Free shipping on orders over $50",
        icon: "🚚",
        iconType: "emoji",
        image: null,
      },
      {
        id: "default-2",
        title: "24/7 Support",
        description: "Round-the-clock customer support",
        icon: "💬",
        iconType: "emoji",
        image: null,
      },
      {
        id: "default-3",
        title: "Easy Returns",
        description: "30-day hassle-free returns",
        icon: "↩️",
        iconType: "emoji",
        image: null,
      },
      {
        id: "default-4",
        title: "Secure Payment",
        description: "Your payment information is safe",
        icon: "🔒",
        iconType: "emoji",
        image: null,
      },
    ],
  };
}

function getDefaultSectionBackgrounds() {
  return {
    hero: "transparent",
    benefits: "#f8fafc",
    trending: "#f1f5f9",
    categories: "#f8fafc",
    limitedOffer: "linear-gradient(to right, #3b82f6, #8b5cf6)",
    newArrivals: "#f1f5f9",
    newsletter: "#f8fafc",
  };
}

function getDefaultCardsDesign() {
  return {
    borderRadius: "rounded-lg",
    shadow: "shadow-xl",
    backgroundColor: "#ffffff",
    padding: "p-6",
  };
}

function getDefaultTypographyDesign() {
  return {
    // DaisyUI default typography - no custom styles applied
    h1: {
      fontFamily: "inherit", // Uses DaisyUI default
      fontSize: "inherit", // Uses DaisyUI default
      fontWeight: "inherit", // Uses DaisyUI default
      color: "inherit", // Uses DaisyUI default
    },
    h2: {
      fontFamily: "inherit",
      fontSize: "inherit",
      fontWeight: "inherit",
      color: "inherit",
    },
    h3: {
      fontFamily: "inherit",
      fontSize: "inherit",
      fontWeight: "inherit",
      color: "inherit",
    },
    h4: {
      fontFamily: "inherit",
      fontSize: "inherit",
      fontWeight: "inherit",
      color: "inherit",
    },
  };
}

function getDefaultPageDesign() {
  return {
    backgroundColor: "inherit", // Uses DaisyUI default
    backgroundType: "color",
    gradient: { from: "#ffffff", to: "#f3f4f6", direction: "to-b" },
    backgroundImage: null,
    overlay: { type: "none", color: "#000000", opacity: 0.5 },
  };
}

function getDefaultButtonDesign() {
  return {
    primary: {
      style: "default", // Uses DaisyUI default
      size: "default",
      colors: { bg: "inherit", text: "inherit", border: "inherit" }, // Uses DaisyUI defaults
    },
    secondary: {
      style: "default",
      size: "default",
      colors: { bg: "inherit", text: "inherit", border: "inherit" },
    },
  };
}
