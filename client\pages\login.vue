<template>
  <div
    class="min-h-screen bg-base-200 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8"
  >
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <NuxtLink to="/" class="btn btn-ghost text-2xl font-bold">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-8 w-8"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
            />
          </svg>
          Multi Store
        </NuxtLink>
        <h2 class="mt-6 text-3xl font-bold">Admin Login</h2>
        <p class="mt-2 text-sm text-base-content/70">
          Sign in to access the admin dashboard
        </p>
      </div>

      <!-- Login Form -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <form @submit.prevent="handleLogin" class="space-y-6">
            <!-- Email Field -->
            <div class="form-control">
              <label class="label">
                <span class="label-text">Email address</span>
              </label>
              <input
                v-model="form.email"
                type="email"
                placeholder="Enter your email"
                class="input input-bordered w-full"
                :class="{ 'input-error': errors.email }"
                required
                autocomplete="email"
              />
              <label v-if="errors.email" class="label">
                <span class="label-text-alt text-error">{{
                  errors.email
                }}</span>
              </label>
            </div>

            <!-- Password Field -->
            <div class="form-control">
              <label class="label">
                <span class="label-text">Password</span>
              </label>
              <input
                v-model="form.password"
                type="password"
                placeholder="Enter your password"
                class="input input-bordered w-full"
                :class="{ 'input-error': errors.password }"
                required
                autocomplete="current-password"
              />
              <label v-if="errors.password" class="label">
                <span class="label-text-alt text-error">{{
                  errors.password
                }}</span>
              </label>
            </div>

            <!-- Remember Me -->
            <div class="form-control">
              <label class="label cursor-pointer justify-start gap-3">
                <input
                  v-model="form.rememberMe"
                  type="checkbox"
                  class="checkbox checkbox-primary"
                />
                <span class="label-text">Remember me</span>
              </label>
            </div>

            <!-- Error Message -->
            <div v-if="error" class="alert alert-error">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="stroke-current shrink-0 h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span>{{ error }}</span>
            </div>

            <!-- Submit Button -->
            <div class="form-control">
              <button
                type="submit"
                class="btn btn-primary w-full"
                :class="{ loading: loading }"
                :disabled="loading"
              >
                <span v-if="!loading">Sign In</span>
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Footer -->
      <div class="text-center">
        <p class="text-sm text-base-content/70">
          Don't have admin access?
          <NuxtLink to="/" class="link link-primary">
            Return to store
          </NuxtLink>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
// Use no layout for login page
definePageMeta({
  layout: false,
});

// Meta tags
useHead({
  title: "Admin Login - Multi Store",
  meta: [
    { name: "description", content: "Admin login for Multi Store dashboard" },
  ],
});

// Redirect if already authenticated
const authStore = useAuthStore();

// Also watch for changes in authentication state
watch(
  () => authStore.isAuthenticated,
  (isAuthenticated) => {
    if (isAuthenticated && authStore.isAdmin) {
      navigateTo("/admin");
    }
  }
);

// Form state
const form = reactive({
  email: "",
  password: "",
  rememberMe: false,
});

const loading = ref(false);
const error = ref("");
const errors = reactive({
  email: "",
  password: "",
});

// Clear errors when form changes
watch(
  () => form.email,
  () => {
    errors.email = "";
    error.value = "";
  }
);

watch(
  () => form.password,
  () => {
    errors.password = "";
    error.value = "";
  }
);

// Handle login
const handleLogin = async () => {
  // Reset errors
  errors.email = "";
  errors.password = "";
  error.value = "";

  // Basic validation
  if (!form.email) {
    errors.email = "Email is required";
    return;
  }

  if (!form.password) {
    errors.password = "Password is required";
    return;
  }

  loading.value = true;

  try {
    await authStore.login({
      email: form.email,
      password: form.password,
      rememberMe: form.rememberMe,
    });

    // Check if user has admin privileges
    if (authStore.isAdmin) {
      await nextTick(); // Ensure state is fully updated
      await navigateTo("/admin");
    } else {
      error.value = "Access denied. Admin privileges required.";
      authStore.logout();
    }
  } catch (err) {
    console.error("Login error:", err);

    if (err.status === 401) {
      error.value = "Invalid email or password";
    } else if (err.status === 403) {
      error.value = "Access denied. Admin privileges required.";
    } else {
      error.value = "Login failed. Please try again.";
    }
  } finally {
    loading.value = false;
  }
};

// Initialize page
onMounted(async () => {
  // Wait a bit for the auth plugin to initialize
  await nextTick();

  // Check if user is already authenticated and is admin
  if (authStore.isAuthenticated && authStore.isAdmin) {
    await navigateTo("/admin");
    return;
  }

  // Pre-fill with demo credentials for development
  if (process.env.NODE_ENV === "development") {
    form.email = "<EMAIL>";
    form.password = "Onamission#007";
  }
});
</script>
