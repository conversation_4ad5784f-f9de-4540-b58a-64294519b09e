{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"], "ts-node": {"transpileOnly": true, "files": true}}