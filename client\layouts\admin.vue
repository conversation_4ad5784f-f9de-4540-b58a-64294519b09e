<template>
  <div class="min-h-screen bg-base-200">
    <!-- Admin Navbar -->
    <div class="navbar bg-base-100 shadow-sm">
      <div class="navbar-start">
        <NuxtLink to="/" class="btn btn-ghost text-xl">
          <div
            v-if="companySettings?.logoUrl"
            :style="logoImageStyle"
            class="mr-2"
          >
            <NuxtImg
              :src="companySettings.logoUrl"
              :alt="companySettings.companyName || 'Store Logo'"
              class="w-full h-full object-contain"
              preset="logo"
            />
          </div>
          <svg
            v-else
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
            />
          </svg>
          <span
            v-if="companySettings?.logoText"
            :style="logoTextStyle"
            class="leading-none"
          >
            {{ companySettings.logoText }}
          </span>
          <!-- Show company name only if no logo image AND no logo text -->
          <span v-else-if="!companySettings?.logoUrl">
            {{ companySettings?.companyName || "Multi Store" }}
          </span>
          Admin
        </NuxtLink>
      </div>

      <div class="navbar-center">
        <div class="breadcrumbs text-sm">
          <ul>
            <li><NuxtLink to="/admin">Admin</NuxtLink></li>
            <li v-if="$route.path !== '/admin'">{{ pageTitle }}</li>
          </ul>
        </div>
      </div>

      <div class="navbar-end">
        <div class="dropdown dropdown-end">
          <div
            tabindex="0"
            role="button"
            class="btn btn-ghost btn-circle avatar"
          >
            <div
              class="w-10 rounded-full bg-primary text-primary-content flex items-center justify-center"
            >
              {{ authStore.user?.firstName?.charAt(0) || "A" }}
            </div>
          </div>
          <ul
            tabindex="0"
            class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow"
          >
            <li>
              <a class="justify-between">
                {{ authStore.fullName || authStore.user?.email }}
                <span class="badge badge-primary badge-sm">{{
                  authStore.user?.role
                }}</span>
              </a>
            </li>
            <li><NuxtLink to="/">View Store</NuxtLink></li>
            <li><button @click="authStore.logout()">Logout</button></li>
          </ul>
        </div>
      </div>
    </div>

    <div class="flex">
      <!-- Sidebar -->
      <div class="w-64 min-h-screen bg-base-100 shadow-sm">
        <div class="p-4">
          <ul class="menu">
            <li class="menu-title">
              <span>Dashboard</span>
            </li>
            <li>
              <NuxtLink
                to="/admin"
                :class="{ active: $route.path === '/admin' }"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z"
                  />
                </svg>
                Overview
              </NuxtLink>
            </li>

            <li class="menu-title">
              <span>Store Management</span>
            </li>
            <li>
              <NuxtLink
                to="/admin/products"
                :class="{ active: $route.path.startsWith('/admin/products') }"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
                  />
                </svg>
                Products
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/admin/categories"
                :class="{ active: $route.path.startsWith('/admin/categories') }"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                  />
                </svg>
                Categories
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/admin/orders"
                :class="{ active: $route.path.startsWith('/admin/orders') }"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                Orders
              </NuxtLink>
            </li>

            <li class="menu-title">
              <span>Content</span>
            </li>
            <li>
              <NuxtLink
                to="/admin/content"
                :class="{ active: $route.path.startsWith('/admin/content') }"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                  />
                </svg>
                Homepage
              </NuxtLink>
            </li>
            <li>
              <NuxtLink
                to="/admin/settings"
                :class="{ active: $route.path.startsWith('/admin/settings') }"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
                Settings
              </NuxtLink>
            </li>
          </ul>
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 p-6">
        <slot />
      </div>
    </div>
  </div>
</template>

<script setup>
const authStore = useAuthStore();
const contentStore = useContentStore();
const route = useRoute();

// Get company settings for logo and name
const companySettings = computed(() => contentStore.companySettings);

// Fetch company settings on mount
onMounted(async () => {
  try {
    if (!contentStore.companySettings) {
      await contentStore.fetchCompanySettings();
    }
  } catch (error) {
    console.error("Error loading company settings in admin layout:", error);
  }
});

// Generate page title from route
const pageTitle = computed(() => {
  const path = route.path;
  if (path === "/admin") return "Dashboard";

  const segments = path.split("/").filter(Boolean);
  if (segments.length > 1) {
    return segments[segments.length - 1]
      .split("-")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  }

  return "Admin";
});

// Computed style for logo text
const logoTextStyle = computed(() => {
  if (!companySettings.value?.logoTextStyles) return {};

  const styles = companySettings.value.logoTextStyles;
  return {
    fontFamily: styles.fontFamily,
    fontSize: `${styles.fontSize}px`,
    fontWeight: styles.fontWeight,
    fontStyle: styles.fontStyle,
    color: styles.color,
    marginTop: `${styles.marginTop || 0}px`,
    marginRight: `${styles.marginRight || 0}px`,
    marginBottom: `${styles.marginBottom || 0}px`,
    marginLeft: `${styles.marginLeft || 0}px`,
    paddingTop: `${styles.paddingTop || 0}px`,
    paddingRight: `${styles.paddingRight || 0}px`,
    paddingBottom: `${styles.paddingBottom || 0}px`,
    paddingLeft: `${styles.paddingLeft || 0}px`,
  };
});

// Computed style for logo image
const logoImageStyle = computed(() => {
  if (!companySettings.value?.logoImageStyles)
    return { width: "24px", height: "24px" };

  const styles = companySettings.value.logoImageStyles;
  return {
    width: `${styles.width || 24}px`,
    height: styles.maintainAspectRatio
      ? `${styles.width || 24}px`
      : `${styles.height || 24}px`,
    marginTop: `${styles.marginTop || 0}px`,
    marginRight: `${styles.marginRight || 0}px`,
    marginBottom: `${styles.marginBottom || 0}px`,
    marginLeft: `${styles.marginLeft || 0}px`,
    paddingTop: `${styles.paddingTop || 0}px`,
    paddingRight: `${styles.paddingRight || 0}px`,
    paddingBottom: `${styles.paddingBottom || 0}px`,
    paddingLeft: `${styles.paddingLeft || 0}px`,
  };
});
</script>

<style scoped>
.menu li > a.active {
  @apply bg-primary text-primary-content;
}
</style>
