// Generated by Nuxt'
import type { Plugin } from '#app'

type Decorate<T extends Record<string, any>> = { [K in keyof T as K extends string ? `$${K}` : never]: T[K] }

type InjectionType<A extends Plugin> = A extends {default: Plugin<infer T>} ? Decorate<T> : unknown

type NuxtAppInjections = 
  InjectionType<typeof import("../../node_modules/.pnpm/@pinia+nuxt@0.11.1_magicast@0.3.5_pinia@3.0.3_typescript@5.8.3_vue@3.5.17_typescript@5.8.3__/node_modules/@pinia/nuxt/dist/runtime/payload-plugin.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/plugins/revive-payload.client.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/plugins/unhead.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/pages/runtime/plugins/router.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/plugins/browser-devtools-timing.client.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/plugins/navigation-repaint.client.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/plugins/check-outdated-build.client.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/plugins/revive-payload.server.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/plugins/chunk-reload.client.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/@pinia+nuxt@0.11.1_magicast@0.3.5_pinia@3.0.3_typescript@5.8.3_vue@3.5.17_typescript@5.8.3__/node_modules/@pinia/nuxt/dist/runtime/plugin.vue3.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/pages/runtime/plugins/prefetch.client.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/pages/runtime/plugins/check-if-page-unused.js")> &
  InjectionType<typeof import("../../../../../../../../AppData/Roaming/npm/node_modules/@nuxt/devtools/dist/runtime/plugins/devtools.server.js")> &
  InjectionType<typeof import("../../../../../../../../AppData/Roaming/npm/node_modules/@nuxt/devtools/dist/runtime/plugins/devtools.client.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/@nuxt+icon@1.15.0_magicast@0.3.5_vite@6.3.5_@types+node@24.0.13_jiti@2.4.2_terser@5.43.1_yaml_wewk6rl4tdmtvypb2ndrpw4rxe/node_modules/@nuxt/icon/dist/runtime/plugin.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/pinia-plugin-persistedstate@4.4.1_@nuxt+kit@3.17.7_magicast@0.3.5__@pinia+nuxt@0.11.1_magicas_eyi6v366vwczah6f7v6tybvmga/node_modules/pinia-plugin-persistedstate/dist/nuxt/runtime/plugin.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/plugins/dev-server-logs.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/plugins/check-if-layout-used.js")> &
  InjectionType<typeof import("../../plugins/auth.client")>

declare module '#app' {
  interface NuxtApp extends NuxtAppInjections { }

  interface NuxtAppLiterals {
    pluginName: 'nuxt:revive-payload:client' | 'nuxt:head' | 'nuxt:router' | 'nuxt:browser-devtools-timing' | 'nuxt:revive-payload:server' | 'nuxt:chunk-reload' | 'pinia' | 'nuxt:global-components' | 'nuxt:prefetch' | 'nuxt:checkIfPageUnused' | '@nuxt/icon' | 'pinia-plugin-persistedstate' | 'nuxt:checkIfLayoutUsed' | 'auth-init'
  }
}

declare module 'vue' {
  interface ComponentCustomProperties extends NuxtAppInjections { }
}

export { }
