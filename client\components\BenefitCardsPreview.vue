<template>
  <div :class="getGridClasses()">
    <div 
      v-for="card in design.cards" 
      :key="card.id"
      class="card bg-base-100 shadow-xl"
    >
      <div class="card-body text-center">
        <!-- Icon or Image -->
        <div class="mx-auto mb-4">
          <div v-if="card.iconType === 'emoji' && card.icon" class="text-4xl">
            {{ card.icon }}
          </div>
          <div v-else-if="card.iconType === 'image' && card.image" class="avatar">
            <div class="w-16 rounded-full">
              <NuxtImg :src="card.image" :alt="card.title" />
            </div>
          </div>
          <div v-else class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary-content" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
        </div>
        
        <!-- Title -->
        <h3 class="card-title justify-center text-lg">{{ card.title || 'Card Title' }}</h3>
        
        <!-- Description -->
        <p class="text-base-content/70">{{ card.description || 'Card description goes here' }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  design: {
    type: Object,
    required: true,
  },
});

// Responsive grid classes based on number of cards
const getGridClasses = () => {
  const cardCount = props.design.cards.length;
  
  if (cardCount === 1) {
    return "grid grid-cols-1 max-w-sm mx-auto gap-6";
  } else if (cardCount === 2) {
    return "grid grid-cols-1 md:grid-cols-2 max-w-2xl mx-auto gap-6";
  } else if (cardCount === 3) {
    return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 max-w-4xl mx-auto gap-6";
  } else if (cardCount === 4) {
    return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 max-w-6xl mx-auto gap-6";
  } else if (cardCount === 5) {
    return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 max-w-7xl mx-auto gap-6";
  } else if (cardCount === 6) {
    return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 max-w-7xl mx-auto gap-6";
  }
  
  // Default fallback
  return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6";
};
</script>
