<template>
  <div :class="['min-h-screen bg-base-100', ...pageClasses]">
    <!-- Navigation -->
    <AppNavbar />

    <!-- Main Content -->
    <main :class="mainClasses">
      <slot />
    </main>

    <!-- Footer -->
    <AppFooter />

    <!-- Cart Drawer -->
    <CartDrawer />

    <!-- Modals and Overlays -->
    <div id="modal-root"></div>
  </div>
</template>

<script setup>
// Initialize stores
const authStore = useAuthStore();
const contentStore = useContentStore();

// Initialize dynamic styling
const { initializeDynamicStyling } = useHomepageDesign();

// Get page-specific classes
const { getPageClasses, getMainClasses } = usePageClasses();
const pageClasses = getPageClasses;
const mainClasses = getMainClasses;

// Initialize auth and content on app start
onMounted(async () => {
  if (authStore.token) {
    authStore.initializeAuth();
  }

  // Ensure company settings are loaded for navbar
  try {
    if (!contentStore.companySettings) {
      await contentStore.fetchCompanySettings();
    }

    // Load homepage design settings
    if (!contentStore.homepageDesign.navbarDesign) {
      await contentStore.fetchHomepageDesign();
    }

    // Initialize dynamic styling after loading design settings
    initializeDynamicStyling();
  } catch (error) {
    console.error("Error loading company settings in default layout:", error);
  }
});
</script>
