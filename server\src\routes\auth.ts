import express from "express";
import {
  login,
  register,
  getProfile,
  updateProfile,
  changePassword,
} from "../controllers/auth.controller";
import { authenticate } from "../middleware/auth";
import { wrapController } from "../utils/wrapController";

const router: express.Router = express.Router();

// Authentication routes
router.post("/login", wrapController(login));
router.post("/register", wrapController(register));

// Profile routes
router.get("/me", authenticate, wrapController(getProfile));
router.put("/profile", authenticate, wrapController(updateProfile));
router.post("/change-password", authenticate, wrapController(changePassword));

export default router;
