<template>
  <div class="min-h-screen bg-base-100">
    <AppNavbar />
    
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Breadcrumbs -->
      <div class="breadcrumbs text-sm mb-8">
        <ul>
          <li><NuxtLink to="/">Home</NuxtLink></li>
          <li><NuxtLink to="/legal">Legal</NuxtLink></li>
          <li>{{ pageTitle }}</li>
        </ul>
      </div>

      <div v-if="pending" class="flex justify-center items-center min-h-96">
        <span class="loading loading-spinner loading-lg"></span>
      </div>

      <div v-else-if="error" class="text-center py-16">
        <div class="alert alert-error max-w-md mx-auto">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>Page not found</span>
        </div>
        <NuxtLink to="/" class="btn btn-primary mt-4">
          Return to Home
        </NuxtLink>
      </div>

      <div v-else class="prose max-w-none">
        <h1>{{ pageTitle }}</h1>
        <div class="text-sm text-base-content/70 mb-8">
          Last updated: {{ lastUpdated }}
        </div>
        <div v-html="content"></div>
      </div>
    </div>

    <AppFooter />
    <CartDrawer />
  </div>
</template>

<script setup>
const route = useRoute()
const slug = route.params.slug

// Page titles mapping
const pageTitles = {
  'terms-of-service': 'Terms of Service',
  'privacy-policy': 'Privacy Policy',
  'delivery-information': 'Delivery Information',
  'return-policy': 'Return Policy',
  'cookie-policy': 'Cookie Policy'
}

const pageTitle = computed(() => pageTitles[slug] || slug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()))

// Mock content for development
const mockContent = {
  'terms-of-service': `
    <h2>1. Acceptance of Terms</h2>
    <p>By accessing and using this website, you accept and agree to be bound by the terms and provision of this agreement.</p>
    
    <h2>2. Use License</h2>
    <p>Permission is granted to temporarily download one copy of the materials on Multi Store's website for personal, non-commercial transitory viewing only.</p>
    
    <h2>3. Disclaimer</h2>
    <p>The materials on Multi Store's website are provided on an 'as is' basis. Multi Store makes no warranties, expressed or implied.</p>
    
    <h2>4. Limitations</h2>
    <p>In no event shall Multi Store or its suppliers be liable for any damages arising out of the use or inability to use the materials on Multi Store's website.</p>
    
    <h2>5. Contact Information</h2>
    <p>If you have any questions about these Terms of Service, please contact <NAME_EMAIL></p>
  `,
  'privacy-policy': `
    <h2>Information We Collect</h2>
    <p>We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us.</p>
    
    <h2>How We Use Your Information</h2>
    <p>We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.</p>
    
    <h2>Information Sharing</h2>
    <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>
    
    <h2>Data Security</h2>
    <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
    
    <h2>Contact Us</h2>
    <p>If you have questions about this Privacy Policy, please contact <NAME_EMAIL></p>
  `,
  'delivery-information': `
    <h2>Shipping Methods</h2>
    <p>We offer several shipping options to meet your needs:</p>
    <ul>
      <li>Standard Shipping (5-7 business days) - Free on orders over $50</li>
      <li>Express Shipping (2-3 business days) - $9.99</li>
      <li>Overnight Shipping (1 business day) - $19.99</li>
    </ul>
    
    <h2>Processing Time</h2>
    <p>Orders are typically processed within 1-2 business days. You will receive a confirmation email with tracking information once your order ships.</p>
    
    <h2>International Shipping</h2>
    <p>We currently ship to select international destinations. Shipping costs and delivery times vary by location.</p>
    
    <h2>Delivery Issues</h2>
    <p>If you experience any issues with your delivery, please contact our customer service <NAME_EMAIL></p>
  `
}

// Fetch legal page data
const { data: pageData, pending, error } = await useLazyAsyncData(`legal-${slug}`, async () => {
  try {
    const contentStore = useContentStore()
    const page = await contentStore.fetchLegalPage(slug)
    return page
  } catch (err) {
    // Return mock data for development
    if (process.dev && mockContent[slug]) {
      return {
        title: pageTitle.value,
        content: mockContent[slug],
        lastUpdated: new Date().toLocaleDateString()
      }
    }
    throw err
  }
})

const content = computed(() => pageData.value?.content || '')
const lastUpdated = computed(() => pageData.value?.lastUpdated || new Date().toLocaleDateString())

// Meta tags
useHead({
  title: () => `${pageTitle.value} - Multi Store`,
  meta: [
    { name: 'description', content: () => `${pageTitle.value} for Multi Store` }
  ]
})
</script>

<style scoped>
.prose {
  @apply text-base-content;
}

.prose h1 {
  @apply text-3xl font-bold mb-6;
}

.prose h2 {
  @apply text-2xl font-semibold mt-8 mb-4;
}

.prose h3 {
  @apply text-xl font-semibold mt-6 mb-3;
}

.prose p {
  @apply mb-4 leading-relaxed;
}

.prose ul {
  @apply list-disc list-inside mb-4 space-y-2;
}

.prose li {
  @apply ml-4;
}
</style>
