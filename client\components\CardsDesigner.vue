<template>
  <div class="space-y-8">
    <!-- Section Header -->
    <div class="border-b pb-4">
      <h2 class="text-2xl font-semibold">Cards Design</h2>
      <p class="text-base-content/70 mt-1">
        Customize the appearance of cards throughout your website
      </p>
    </div>

    <!-- Card Styling -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h3 class="card-title text-lg">Card Styling</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Border Radius -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Border Radius</span>
            </label>
            <select
              v-model="localDesign.borderRadius"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="rounded-none">Sharp Corners</option>
              <option value="rounded-sm">Small Radius</option>
              <option value="rounded">Default Radius</option>
              <option value="rounded-lg">Large Radius</option>
              <option value="rounded-xl">Extra Large Radius</option>
              <option value="rounded-2xl">2X Large Radius</option>
              <option value="rounded-full">Fully Rounded</option>
            </select>
          </div>

          <!-- Shadow -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Shadow</span>
            </label>
            <select
              v-model="localDesign.shadow"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="shadow-none">No Shadow</option>
              <option value="shadow-sm">Small Shadow</option>
              <option value="shadow">Default Shadow</option>
              <option value="shadow-md">Medium Shadow</option>
              <option value="shadow-lg">Large Shadow</option>
              <option value="shadow-xl">Extra Large Shadow</option>
              <option value="shadow-2xl">2X Large Shadow</option>
            </select>
          </div>

          <!-- Background Color -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Background Color</span>
            </label>
            <input
              v-model="localDesign.backgroundColor"
              type="color"
              class="input input-bordered w-20 h-12"
              @input="updateDesign"
            />
          </div>

          <!-- Padding -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Padding</span>
            </label>
            <select
              v-model="localDesign.padding"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="p-2">Small (8px)</option>
              <option value="p-4">Default (16px)</option>
              <option value="p-6">Large (24px)</option>
              <option value="p-8">Extra Large (32px)</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Preview -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h3 class="card-title text-lg">Preview</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Sample Cards -->
          <div
            v-for="i in 3"
            :key="i"
            :class="[
              'card',
              localDesign.borderRadius,
              localDesign.shadow,
              localDesign.padding,
            ]"
            :style="{ backgroundColor: localDesign.backgroundColor }"
          >
            <div class="card-body">
              <h2 class="card-title">Sample Card {{ i }}</h2>
              <p>
                This is a preview of how your cards will look with the current
                settings.
              </p>
              <div class="card-actions justify-end">
                <button class="btn btn-primary btn-sm">Action</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  design: {
    type: Object,
    required: true,
  },
});

// Emits
const emit = defineEmits(["update:design", "update"]);

// Local state
const localDesign = ref({
  borderRadius: "rounded-lg",
  shadow: "shadow-xl",
  backgroundColor: "#ffffff",
  padding: "p-6",
  ...(props.design || {}),
});

// Methods
const updateDesign = () => {
  emit("update:design", { ...localDesign.value });
  emit("update");
};

// Watch for prop changes
watch(
  () => props.design,
  (newDesign) => {
    localDesign.value = { ...newDesign };
  },
  { deep: true }
);
</script>
