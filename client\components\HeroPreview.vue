<template>
  <div
    :class="[
      'carousel carousel-center rounded-box w-full shadow-lg',
      getWidthClass(),
      getPaddingClass(),
      getMarginClass(),
    ]"
    :style="{
      height: design.height,
      ...getBackgroundStyle(),
    }"
  >
    <!-- Sample Slide -->
    <div class="carousel-item relative w-full">
      <!-- Background Image or Color -->
      <div
        v-if="design.slides.length > 0"
        class="w-full h-full"
        :style="getImageStyle(design.slides[0])"
      >
        <img
          :src="design.slides[0].image"
          :alt="design.slides[0].title"
          :class="getImagePositionClass()"
        />
      </div>
      <div
        v-else
        class="w-full h-full flex items-center justify-center"
        :style="getBackgroundStyle()"
      >
        <div class="text-center text-base-content/50">
          <Icon name="heroicons:photo" class="h-16 w-16 mx-auto mb-4" />
          <p>Upload images to see preview</p>
        </div>
      </div>

      <!-- Overlay Content -->
      <div
        class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center"
      >
        <div class="text-center max-w-2xl px-4">
          <h1 class="carousel-title mb-4" :style="getTitleStyles()">
            {{
              design.slides.length > 0
                ? design.slides[0].title || "Sample Hero Title"
                : "Sample Hero Title"
            }}
          </h1>
          <p class="carousel-subtitle mb-8" :style="getSubtitleStyles()">
            {{
              design.slides.length > 0
                ? design.slides[0].subtitle || "Your amazing subtitle goes here"
                : "Your amazing subtitle goes here"
            }}
          </p>
          <button :class="getButtonClasses()" :style="getButtonStyles()">
            {{
              design.slides.length > 0 && design.slides[0].buttonText
                ? design.slides[0].buttonText
                : "Shop Now"
            }}
          </button>
        </div>
      </div>

      <!-- Navigation arrows - positioned at top right -->
      <div class="absolute top-4 right-4 flex gap-2">
        <button
          class="btn btn-circle btn-sm btn-ghost bg-white bg-opacity-20 text-white hover:bg-white hover:bg-opacity-30 backdrop-blur-sm"
        >
          <Icon name="heroicons:chevron-left" class="h-4 w-4" />
        </button>
        <button
          class="btn btn-circle btn-sm btn-ghost bg-white bg-opacity-20 text-white hover:bg-white hover:bg-opacity-30 backdrop-blur-sm"
        >
          <Icon name="heroicons:chevron-right" class="h-4 w-4" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  design: {
    type: Object,
    required: true,
  },
});

// Computed classes and styles
const getWidthClass = () => {
  switch (props.design.width) {
    case "container":
      return "max-w-7xl mx-auto";
    case "lg":
      return "max-w-5xl mx-auto";
    case "md":
      return "max-w-3xl mx-auto";
    case "sm":
      return "max-w-2xl mx-auto";
    default:
      return "w-full";
  }
};

const getPaddingClass = () => {
  const p = props.design.padding;
  return `pt-${p.top} pr-${p.right} pb-${p.bottom} pl-${p.left}`;
};

const getMarginClass = () => {
  const m = props.design.margin;
  return `mt-${m.top} mr-${m.right} mb-${m.bottom} ml-${m.left}`;
};

const getBackgroundStyle = () => {
  const bg = props.design.background;

  if (bg.type === "gradient") {
    const direction = bg.gradient.direction;
    // Convert direction to proper CSS linear-gradient syntax
    const cssDirection = direction
      .replace("to-r", "to right")
      .replace("to-l", "to left")
      .replace("to-b", "to bottom")
      .replace("to-t", "to top")
      .replace("to-br", "to bottom right")
      .replace("to-bl", "to bottom left");

    return {
      background: `linear-gradient(${cssDirection}, ${bg.gradient.from}, ${bg.gradient.to})`,
    };
  }

  return {
    backgroundColor: bg.color,
  };
};

const getImageStyle = (slide) => {
  return {
    position: "relative",
    width: "100%",
    height: "100%",
  };
};

const getImagePositionClass = () => {
  switch (props.design.imagePosition) {
    case "contain":
      return "w-full h-full object-contain";
    case "left":
      return "w-1/2 h-full object-cover float-left";
    case "right":
      return "w-1/2 h-full object-cover float-right";
    case "top-left":
      return "w-1/2 h-1/2 object-cover absolute top-0 left-0";
    case "top-right":
      return "w-1/2 h-1/2 object-cover absolute top-0 right-0";
    case "bottom-left":
      return "w-1/2 h-1/2 object-cover absolute bottom-0 left-0";
    case "bottom-right":
      return "w-1/2 h-1/2 object-cover absolute bottom-0 right-0";
    default:
      return "w-full h-full object-cover";
  }
};

// Text and button styling methods
const getTitleStyles = () => {
  const textDesign = props.design.textDesign?.title;
  if (!textDesign) {
    return {
      fontFamily: "Inter",
      fontSize: "3rem",
      fontWeight: "700",
      color: "#ffffff",
    };
  }

  return {
    fontFamily: textDesign.fontFamily,
    fontSize: `${textDesign.fontSize}px`,
    fontWeight: textDesign.fontWeight,
    color: textDesign.color,
  };
};

const getSubtitleStyles = () => {
  const textDesign = props.design.textDesign?.subtitle;
  if (!textDesign) {
    return {
      fontFamily: "Inter",
      fontSize: "1.125rem",
      fontWeight: "400",
      color: "#ffffff",
    };
  }

  return {
    fontFamily: textDesign.fontFamily,
    fontSize: `${textDesign.fontSize}px`,
    fontWeight: textDesign.fontWeight,
    color: textDesign.color,
  };
};

const getButtonClasses = () => {
  const buttonDesign = props.design.buttonDesign;
  if (!buttonDesign || buttonDesign.style === "custom") {
    return "btn btn-primary btn-lg";
  }

  const classes = ["btn"];

  // Style classes
  switch (buttonDesign.style) {
    case "primary":
      classes.push("btn-primary");
      break;
    case "secondary":
      classes.push("btn-secondary");
      break;
    case "accent":
      classes.push("btn-accent");
      break;
    case "neutral":
      classes.push("btn-neutral");
      break;
    case "info":
      classes.push("btn-info");
      break;
    case "success":
      classes.push("btn-success");
      break;
    case "warning":
      classes.push("btn-warning");
      break;
    case "error":
      classes.push("btn-error");
      break;
    case "outline":
      classes.push("btn-outline");
      break;
    case "ghost":
      classes.push("btn-ghost");
      break;
    case "link":
      classes.push("btn-link");
      break;
    default:
      classes.push("btn-primary");
      break;
  }

  // Size classes
  switch (buttonDesign.size) {
    case "xs":
      classes.push("btn-xs");
      break;
    case "sm":
      classes.push("btn-sm");
      break;
    case "lg":
      classes.push("btn-lg");
      break;
    case "xl":
      classes.push("btn-xl");
      break;
  }

  // Shape classes
  switch (buttonDesign.shape) {
    case "square":
      classes.push("btn-square");
      break;
    case "circle":
      classes.push("btn-circle");
      break;
    case "wide":
      classes.push("btn-wide");
      break;
  }

  return classes.join(" ");
};

const getButtonStyles = () => {
  const buttonDesign = props.design.buttonDesign;
  if (!buttonDesign || buttonDesign.style !== "custom") {
    return {};
  }

  return {
    backgroundColor: buttonDesign.backgroundColor,
    color: buttonDesign.textColor,
    borderColor: buttonDesign.borderColor,
  };
};
</script>
