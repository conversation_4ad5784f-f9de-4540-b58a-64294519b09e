<template>
  <div
    :class="[
      'carousel carousel-center rounded-box w-full shadow-lg',
      getWidthClass(),
      getPaddingClass(),
      getMarginClass(),
    ]"
    :style="{
      height: design.height,
      ...getBackgroundStyle(),
    }"
  >
    <!-- Display all slides or placeholder -->
    <div
      v-if="displaySlides.length === 0"
      class="carousel-item relative w-full"
    >
      <div
        class="w-full h-full flex items-center justify-center"
        :style="getBackgroundStyle()"
      >
        <div class="text-center text-base-content/50">
          <Icon name="heroicons:photo" class="h-16 w-16 mx-auto mb-4" />
          <p>Upload images to see preview</p>
        </div>
      </div>
    </div>

    <!-- Actual slides -->
    <div
      v-for="(slide, index) in displaySlides"
      :key="slide.id || index"
      :id="`preview-slide${index + 1}`"
      class="carousel-item relative w-full"
    >
      <!-- Background Image -->
      <NuxtImg
        v-if="slide.image"
        :src="slide.image"
        :alt="slide.title || 'Hero slide'"
        class="w-full h-full object-cover"
      />

      <!-- Overlay Content -->
      <div
        class="absolute inset-0 flex items-center justify-center"
        :class="slide.image ? 'bg-black bg-opacity-40' : ''"
        :style="!slide.image ? getBackgroundStyle() : {}"
      >
        <div class="text-center max-w-2xl px-4">
          <h1 class="carousel-title mb-4" :style="getTitleStyles()">
            {{ slide.title || "Sample Hero Title" }}
          </h1>
          <p class="carousel-subtitle mb-8" :style="getSubtitleStyles()">
            {{ slide.subtitle || "Your amazing subtitle goes here" }}
          </p>
          <button :class="getButtonClasses()" :style="getButtonStyles()">
            {{ slide.buttonText || "Shop Now" }}
          </button>
        </div>
      </div>

      <!-- Navigation arrows - positioned at top right -->
      <div
        v-if="displaySlides.length > 1"
        class="absolute top-4 right-4 flex gap-2"
      >
        <button
          @click="goToSlide(index === 0 ? displaySlides.length : index)"
          class="btn btn-circle btn-sm btn-ghost bg-white bg-opacity-20 text-white hover:bg-white hover:bg-opacity-30 backdrop-blur-sm"
          title="Previous slide"
        >
          <Icon name="heroicons:chevron-left" class="h-4 w-4" />
        </button>
        <button
          @click="goToSlide(index === displaySlides.length - 1 ? 1 : index + 2)"
          class="btn btn-circle btn-sm btn-ghost bg-white bg-opacity-20 text-white hover:bg-white hover:bg-opacity-30 backdrop-blur-sm"
          title="Next slide"
        >
          <Icon name="heroicons:chevron-right" class="h-4 w-4" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  design: {
    type: Object,
    required: true,
  },
});

// Reactive data
const currentSlide = ref(1);

// Computed properties
const displaySlides = computed(() => {
  return props.design.slides || [];
});

// Navigation functions
const goToSlide = (slideNumber) => {
  const slideElement = document.getElementById(`preview-slide${slideNumber}`);
  if (slideElement) {
    slideElement.scrollIntoView({
      behavior: "smooth",
      block: "nearest",
      inline: "start",
    });
    currentSlide.value = slideNumber;
  }
};

// Computed classes and styles
const getWidthClass = () => {
  switch (props.design.width) {
    case "1920":
      return "max-w-[1920px] mx-auto";
    case "1600":
      return "max-w-[1600px] mx-auto";
    case "1440":
      return "max-w-[1440px] mx-auto";
    case "1200":
      return "max-w-[1200px] mx-auto";
    case "1024":
      return "max-w-[1024px] mx-auto";
    case "768":
      return "max-w-[768px] mx-auto";
    case "640":
      return "max-w-[640px] mx-auto";
    default:
      return "w-full";
  }
};

const getPaddingClass = () => {
  const p = props.design.padding;
  return `pt-${p.top} pr-${p.right} pb-${p.bottom} pl-${p.left}`;
};

const getMarginClass = () => {
  const m = props.design.margin;
  return `mt-${m.top} mr-${m.right} mb-${m.bottom} ml-${m.left}`;
};

const getBackgroundStyle = () => {
  const bg = props.design.background;

  if (bg.type === "gradient") {
    const direction = bg.gradient.direction;
    // Convert direction to proper CSS linear-gradient syntax
    const cssDirection = direction
      .replace("to-r", "to right")
      .replace("to-l", "to left")
      .replace("to-b", "to bottom")
      .replace("to-t", "to top")
      .replace("to-br", "to bottom right")
      .replace("to-bl", "to bottom left");

    return {
      background: `linear-gradient(${cssDirection}, ${bg.gradient.from}, ${bg.gradient.to})`,
    };
  }

  return {
    backgroundColor: bg.color,
  };
};

// Text and button styling methods
const getTitleStyles = () => {
  const textDesign = props.design.textDesign?.title;
  if (!textDesign) {
    return {
      fontFamily: "Inter",
      fontSize: "3rem",
      fontWeight: "700",
      color: "#ffffff",
    };
  }

  return {
    fontFamily: textDesign.fontFamily,
    fontSize: `${textDesign.fontSize}px`,
    fontWeight: textDesign.fontWeight,
    color: textDesign.color,
  };
};

const getSubtitleStyles = () => {
  const textDesign = props.design.textDesign?.subtitle;
  if (!textDesign) {
    return {
      fontFamily: "Inter",
      fontSize: "1.125rem",
      fontWeight: "400",
      color: "#ffffff",
    };
  }

  return {
    fontFamily: textDesign.fontFamily,
    fontSize: `${textDesign.fontSize}px`,
    fontWeight: textDesign.fontWeight,
    color: textDesign.color,
  };
};

const getButtonClasses = () => {
  const buttonDesign = props.design.buttonDesign;
  if (!buttonDesign) {
    return "btn btn-primary btn-lg";
  }

  const classes = ["btn"];

  // Style classes
  if (buttonDesign.style !== "custom") {
    switch (buttonDesign.style) {
      case "primary":
        classes.push("btn-primary");
        break;
      case "secondary":
        classes.push("btn-secondary");
        break;
      case "accent":
        classes.push("btn-accent");
        break;
      case "neutral":
        classes.push("btn-neutral");
        break;
      case "info":
        classes.push("btn-info");
        break;
      case "success":
        classes.push("btn-success");
        break;
      case "warning":
        classes.push("btn-warning");
        break;
      case "error":
        classes.push("btn-error");
        break;
      case "outline":
        classes.push("btn-outline");
        break;
      case "soft":
        classes.push("btn-soft");
        break;
      case "dash":
        classes.push("btn-dash");
        break;
      case "ghost":
        classes.push("btn-ghost");
        break;
      case "link":
        classes.push("btn-link");
        break;
      default:
        classes.push("btn-primary");
        break;
    }
  }

  // Size classes
  switch (buttonDesign.size) {
    case "xs":
      classes.push("btn-xs");
      break;
    case "sm":
      classes.push("btn-sm");
      break;
    case "lg":
      classes.push("btn-lg");
      break;
    case "xl":
      classes.push("btn-xl");
      break;
  }

  // Rounded corner classes
  switch (buttonDesign.rounded) {
    case "none":
      classes.push("rounded-none");
      break;
    case "sm":
      classes.push("rounded-sm");
      break;
    case "md":
      classes.push("rounded-md");
      break;
    case "lg":
      classes.push("rounded-lg");
      break;
    case "xl":
      classes.push("rounded-xl");
      break;
    case "2xl":
      classes.push("rounded-2xl");
      break;
    case "3xl":
      classes.push("rounded-3xl");
      break;
    case "full":
      classes.push("rounded-full");
      break;
    case "circle":
      classes.push("btn-circle");
      break;
  }

  // Modifier classes
  if (buttonDesign.wide) {
    classes.push("btn-wide");
  }

  if (buttonDesign.block) {
    classes.push("btn-block");
  }

  if (buttonDesign.square) {
    classes.push("btn-square");
  }

  if (buttonDesign.dropShadow) {
    classes.push("shadow-lg");
  } else {
    classes.push("shadow-none");
  }

  return classes.join(" ");
};

const getButtonStyles = () => {
  const buttonDesign = props.design.buttonDesign;
  if (!buttonDesign || buttonDesign.style !== "custom") {
    return {};
  }

  return {
    backgroundColor: buttonDesign.backgroundColor,
    color: buttonDesign.textColor,
    borderColor: buttonDesign.borderColor,
  };
};
</script>
