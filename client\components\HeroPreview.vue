<template>
  <div 
    :class="[
      'carousel carousel-center rounded-box w-full shadow-lg',
      getWidthClass(),
      getPaddingClass(),
      getMarginClass()
    ]"
    :style="{ 
      height: design.height,
      ...getBackgroundStyle()
    }"
  >
    <!-- Sample Slide -->
    <div class="carousel-item relative w-full">
      <!-- Background Image or Color -->
      <div 
        v-if="design.slides.length > 0"
        class="w-full h-full"
        :style="getImageStyle(design.slides[0])"
      >
        <img 
          :src="design.slides[0].image" 
          :alt="design.slides[0].title"
          :class="getImagePositionClass()"
        />
      </div>
      <div 
        v-else
        class="w-full h-full flex items-center justify-center"
        :style="getBackgroundStyle()"
      >
        <div class="text-center text-base-content/50">
          <Icon name="heroicons:photo" class="h-16 w-16 mx-auto mb-4" />
          <p>Upload images to see preview</p>
        </div>
      </div>

      <!-- Overlay Content -->
      <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
        <div class="text-center text-white max-w-2xl px-4">
          <h1 class="text-4xl md:text-6xl font-bold mb-4">
            {{ design.slides.length > 0 ? design.slides[0].title || 'Sample Hero Title' : 'Sample Hero Title' }}
          </h1>
          <p class="text-lg md:text-xl mb-8">
            {{ design.slides.length > 0 ? design.slides[0].subtitle || 'Your amazing subtitle goes here' : 'Your amazing subtitle goes here' }}
          </p>
          <button class="btn btn-primary btn-lg">
            Shop Now
          </button>
        </div>
      </div>

      <!-- Navigation arrows - positioned at top right -->
      <div class="absolute top-4 right-4 flex gap-2">
        <button class="btn btn-circle btn-sm btn-ghost bg-white bg-opacity-20 text-white hover:bg-white hover:bg-opacity-30 backdrop-blur-sm">
          <Icon name="heroicons:chevron-left" class="h-4 w-4" />
        </button>
        <button class="btn btn-circle btn-sm btn-ghost bg-white bg-opacity-20 text-white hover:bg-white hover:bg-opacity-30 backdrop-blur-sm">
          <Icon name="heroicons:chevron-right" class="h-4 w-4" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  design: {
    type: Object,
    required: true
  }
});

// Computed classes and styles
const getWidthClass = () => {
  switch (props.design.width) {
    case 'container': return 'max-w-7xl mx-auto';
    case 'lg': return 'max-w-5xl mx-auto';
    case 'md': return 'max-w-3xl mx-auto';
    case 'sm': return 'max-w-2xl mx-auto';
    default: return 'w-full';
  }
};

const getPaddingClass = () => {
  const p = props.design.padding;
  return `pt-${p.top} pr-${p.right} pb-${p.bottom} pl-${p.left}`;
};

const getMarginClass = () => {
  const m = props.design.margin;
  return `mt-${m.top} mr-${m.right} mb-${m.bottom} ml-${m.left}`;
};

const getBackgroundStyle = () => {
  const bg = props.design.background;
  
  if (bg.type === 'gradient') {
    const direction = bg.gradient.direction;
    return {
      background: `linear-gradient(${direction.replace('to-', '')}, ${bg.gradient.from}, ${bg.gradient.to})`
    };
  }
  
  return {
    backgroundColor: bg.color
  };
};

const getImageStyle = (slide) => {
  return {
    position: 'relative',
    width: '100%',
    height: '100%'
  };
};

const getImagePositionClass = () => {
  switch (props.design.imagePosition) {
    case 'contain': return 'w-full h-full object-contain';
    case 'left': return 'w-1/2 h-full object-cover float-left';
    case 'right': return 'w-1/2 h-full object-cover float-right';
    case 'top-left': return 'w-1/2 h-1/2 object-cover absolute top-0 left-0';
    case 'top-right': return 'w-1/2 h-1/2 object-cover absolute top-0 right-0';
    case 'bottom-left': return 'w-1/2 h-1/2 object-cover absolute bottom-0 left-0';
    case 'bottom-right': return 'w-1/2 h-1/2 object-cover absolute bottom-0 right-0';
    default: return 'w-full h-full object-cover';
  }
};
</script>
