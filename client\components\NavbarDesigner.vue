<template>
  <div class="space-y-8">
    <!-- Section Header -->
    <div class="border-b pb-4">
      <h2 class="text-2xl font-semibold">Navigation Bar Design</h2>
      <p class="text-base-content/70 mt-1">
        Customize your navigation bar appearance and behavior
      </p>
    </div>

    <!-- Layout Settings -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h3 class="card-title text-lg">Layout & Spacing</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Content Width -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Content Width</span>
            </label>
            <select
              v-model="localDesign.width"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="full">Full Width</option>
              <option value="1920">Ultra Wide (1920px)</option>
              <option value="1600">Wide (1600px)</option>
              <option value="1440">Large Desktop (1440px)</option>
              <option value="1200">Desktop (1200px)</option>
              <option value="1024">Tablet Landscape (1024px)</option>
              <option value="768">Tablet (768px)</option>
              <option value="640">Mobile Large (640px)</option>
            </select>
          </div>

          <!-- Navigation Alignment -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Navigation Alignment</span>
            </label>
            <div class="flex gap-2">
              <label class="label cursor-pointer">
                <input
                  v-model="localDesign.alignment"
                  type="radio"
                  value="start"
                  class="radio radio-primary"
                  @change="updateDesign"
                />
                <span class="label-text ml-2">Start</span>
              </label>
              <label class="label cursor-pointer">
                <input
                  v-model="localDesign.alignment"
                  type="radio"
                  value="center"
                  class="radio radio-primary"
                  @change="updateDesign"
                />
                <span class="label-text ml-2">Center</span>
              </label>
              <label class="label cursor-pointer">
                <input
                  v-model="localDesign.alignment"
                  type="radio"
                  value="end"
                  class="radio radio-primary"
                  @change="updateDesign"
                />
                <span class="label-text ml-2">End</span>
              </label>
            </div>
          </div>

          <!-- Navbar Height -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Navbar Height</span>
            </label>
            <select
              v-model="localDesign.height"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="compact">Compact (48px)</option>
              <option value="default">Default (64px)</option>
              <option value="large">Large (80px)</option>
              <option value="xl">Extra Large (96px)</option>
            </select>
          </div>

          <!-- Item Spacing -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Item Spacing</span>
            </label>
            <select
              v-model="localDesign.itemSpacing"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="tight">Tight (0.5rem)</option>
              <option value="normal">Normal (1rem)</option>
              <option value="relaxed">Relaxed (1.5rem)</option>
              <option value="loose">Loose (2rem)</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Padding & Margin Controls -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h3 class="card-title text-lg">Padding & Margin</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Padding -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Padding</span>
            </label>
            <div class="grid grid-cols-2 gap-2">
              <div class="form-control">
                <label class="label py-1">
                  <span class="label-text-alt">Top</span>
                </label>
                <input
                  v-model.number="localDesign.padding.top"
                  type="number"
                  min="0"
                  max="100"
                  class="input input-bordered input-sm"
                  @input="updateDesign"
                />
              </div>
              <div class="form-control">
                <label class="label py-1">
                  <span class="label-text-alt">Right</span>
                </label>
                <input
                  v-model.number="localDesign.padding.right"
                  type="number"
                  min="0"
                  max="100"
                  class="input input-bordered input-sm"
                  @input="updateDesign"
                />
              </div>
              <div class="form-control">
                <label class="label py-1">
                  <span class="label-text-alt">Bottom</span>
                </label>
                <input
                  v-model.number="localDesign.padding.bottom"
                  type="number"
                  min="0"
                  max="100"
                  class="input input-bordered input-sm"
                  @input="updateDesign"
                />
              </div>
              <div class="form-control">
                <label class="label py-1">
                  <span class="label-text-alt">Left</span>
                </label>
                <input
                  v-model.number="localDesign.padding.left"
                  type="number"
                  min="0"
                  max="100"
                  class="input input-bordered input-sm"
                  @input="updateDesign"
                />
              </div>
            </div>
          </div>

          <!-- Margin -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Margin</span>
            </label>
            <div class="grid grid-cols-2 gap-2">
              <div class="form-control">
                <label class="label py-1">
                  <span class="label-text-alt">Top</span>
                </label>
                <input
                  v-model.number="localDesign.margin.top"
                  type="number"
                  min="0"
                  max="100"
                  class="input input-bordered input-sm"
                  @input="updateDesign"
                />
              </div>
              <div class="form-control">
                <label class="label py-1">
                  <span class="label-text-alt">Right</span>
                </label>
                <input
                  v-model.number="localDesign.margin.right"
                  type="number"
                  min="0"
                  max="100"
                  class="input input-bordered input-sm"
                  @input="updateDesign"
                />
              </div>
              <div class="form-control">
                <label class="label py-1">
                  <span class="label-text-alt">Bottom</span>
                </label>
                <input
                  v-model.number="localDesign.margin.bottom"
                  type="number"
                  min="0"
                  max="100"
                  class="input input-bordered input-sm"
                  @input="updateDesign"
                />
              </div>
              <div class="form-control">
                <label class="label py-1">
                  <span class="label-text-alt">Left</span>
                </label>
                <input
                  v-model.number="localDesign.margin.left"
                  type="number"
                  min="0"
                  max="100"
                  class="input input-bordered input-sm"
                  @input="updateDesign"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Link Styling -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h3 class="card-title text-lg">Link Styling</h3>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Default State -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Default Style</span>
            </label>
            <select
              v-model="localDesign.linkStyle"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="default">Default Button</option>
              <option value="ghost">Ghost</option>
              <option value="link">Clean Link</option>
              <option value="outline">Outline</option>
              <option value="underline">Underlined</option>
            </select>
          </div>

          <!-- Hover State -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Hover Style</span>
            </label>
            <select
              v-model="localDesign.hoverStyle"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="default">Default Hover</option>
              <option value="scale">Scale Up</option>
              <option value="glow">Glow Effect</option>
              <option value="underline">Bottom Border</option>
              <option value="background">Background Change</option>
              <option value="none">No Hover Effect</option>
            </select>
          </div>

          <!-- Active State -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Active Style</span>
            </label>
            <select
              v-model="localDesign.activeStyle"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="default">Default Active</option>
              <option value="primary">Primary Color</option>
              <option value="underline">Bottom Border</option>
              <option value="background">Background Highlight</option>
              <option value="bold">Bold Text</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Text Styling -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h3 class="card-title text-lg">Text Styling</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Font Family -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Font Family</span>
            </label>
            <select
              v-model="localDesign.textStyles.fontFamily"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="Inter">Inter</option>
              <option value="Roboto">Roboto</option>
              <option value="Open Sans">Open Sans</option>
              <option value="Montserrat">Montserrat</option>
              <option value="Poppins">Poppins</option>
            </select>
          </div>

          <!-- Font Size -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Font Size</span>
            </label>
            <input
              v-model.number="localDesign.textStyles.fontSize"
              type="number"
              min="12"
              max="32"
              class="input input-bordered"
              @input="updateDesign"
            />
          </div>

          <!-- Font Weight -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Font Weight</span>
            </label>
            <select
              v-model="localDesign.textStyles.fontWeight"
              class="select select-bordered"
              @change="updateDesign"
            >
              <option value="300">Light</option>
              <option value="400">Normal</option>
              <option value="500">Medium</option>
              <option value="600">Semi Bold</option>
              <option value="700">Bold</option>
            </select>
          </div>

          <!-- Color -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Color</span>
            </label>
            <input
              v-model="localDesign.textStyles.color"
              type="color"
              class="input input-bordered w-20 h-12"
              @input="updateDesign"
            />
          </div>
        </div>

        <!-- Preview -->
        <div class="mt-4 p-4 bg-base-200 rounded-lg">
          <span
            :style="{
              fontFamily: localDesign.textStyles.fontFamily,
              fontSize: `${localDesign.textStyles.fontSize}px`,
              fontWeight: localDesign.textStyles.fontWeight,
              color: localDesign.textStyles.color,
            }"
          >
            {{ sampleText }}
          </span>
        </div>
      </div>
    </div>

    <!-- Preview -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h3 class="card-title text-lg">Preview</h3>
        <div class="mockup-browser border bg-base-300">
          <div class="mockup-browser-toolbar">
            <div class="input">https://yourstore.com</div>
          </div>
          <div class="bg-base-200 px-4 py-2">
            <NavbarPreview :design="localDesign" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Default design settings
const getDefaultDesign = () => ({
  width: "full",
  alignment: "center",
  height: "default",
  padding: { top: 0, right: 0, bottom: 0, left: 0 },
  margin: { top: 0, right: 0, bottom: 0, left: 0 },
  itemSpacing: "normal",
  linkStyle: "default",
  hoverStyle: "default",
  activeStyle: "default",
  textStyles: {
    fontFamily: "Inter",
    fontSize: 16,
    fontWeight: "500",
    color: "#000000",
  },
});

// Props
const props = defineProps({
  design: {
    type: Object,
    required: true,
  },
});

// Emits
const emit = defineEmits(["update:design", "update"]);

// Local state
const localDesign = ref({ ...getDefaultDesign(), ...(props.design || {}) });
const sampleText = ref("Sample Navigation Link");

// Methods
const updateDesign = () => {
  emit("update:design", { ...localDesign.value });
  emit("update");
};

// Watch for prop changes
watch(
  () => props.design,
  (newDesign) => {
    if (newDesign) {
      localDesign.value = { ...getDefaultDesign(), ...newDesign };
    }
  },
  { deep: true }
);
</script>
