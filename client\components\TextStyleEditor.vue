<template>
  <div class="space-y-4">
    <!-- Text Input -->
    <div class="form-control">
      <label class="label">
        <span class="label-text font-medium">Logo Text</span>
        <span class="label-text-alt text-base-content/60"
          >Optional display text next to logo</span
        >
      </label>
      <input
        v-model="localText"
        type="text"
        placeholder="Enter logo text (leave empty for logo only)"
        class="input input-bordered w-full"
        @input="updateText"
      />
    </div>

    <!-- Style Controls (only show if text is entered) -->
    <div v-if="localText" class="space-y-4">
      <div class="divider">Text Styling</div>

      <!-- Font Family -->
      <div class="form-control">
        <label class="label">
          <span class="label-text font-medium">Font Family</span>
        </label>
        <select
          v-model="localStyles.fontFamily"
          class="select select-bordered w-full"
          @change="updateStyles"
        >
          <!-- System Fonts -->
          <optgroup label="System Fonts">
            <option value="Inter">Inter (Default)</option>
            <option value="Arial">Arial</option>
            <option value="Helvetica">Helvetica</option>
            <option value="Georgia">Georgia</option>
            <option value="Times New Roman">Times New Roman</option>
            <option value="Courier New">Courier New</option>
            <option value="Verdana">Verdana</option>
            <option value="Trebuchet MS">Trebuchet MS</option>
            <option value="Impact">Impact</option>
            <option value="Tahoma">Tahoma</option>
            <option value="Palatino">Palatino</option>
            <option value="Garamond">Garamond</option>
          </optgroup>

          <!-- Google Fonts -->
          <optgroup label="Google Fonts">
            <option value="Roboto">Roboto</option>
            <option value="Open Sans">Open Sans</option>
            <option value="Lato">Lato</option>
            <option value="Montserrat">Montserrat</option>
            <option value="Poppins">Poppins</option>
            <option value="Source Sans Pro">Source Sans Pro</option>
            <option value="Oswald">Oswald</option>
            <option value="Raleway">Raleway</option>
            <option value="PT Sans">PT Sans</option>
            <option value="Nunito">Nunito</option>
            <option value="Ubuntu">Ubuntu</option>
            <option value="Mukti">Mukti</option>
            <option value="Playfair Display">Playfair Display</option>
            <option value="Merriweather">Merriweather</option>
            <option value="Crimson Text">Crimson Text</option>
            <option value="Libre Baskerville">Libre Baskerville</option>
            <option value="Abril Fatface">Abril Fatface</option>
            <option value="Bebas Neue">Bebas Neue</option>
            <option value="Dancing Script">Dancing Script</option>
            <option value="Pacifico">Pacifico</option>
            <option value="Lobster">Lobster</option>
            <option value="Righteous">Righteous</option>
            <option value="Bangers">Bangers</option>
            <option value="Fredoka One">Fredoka One</option>
          </optgroup>
        </select>
      </div>

      <!-- Font Size -->
      <div class="form-control">
        <label class="label">
          <span class="label-text font-medium">Font Size</span>
          <span class="label-text-alt">{{ localStyles.fontSize }}px</span>
        </label>
        <input
          v-model.number="localStyles.fontSize"
          type="range"
          min="12"
          max="48"
          class="range range-primary"
          @input="updateStyles"
        />
        <div class="w-full flex justify-between text-xs px-2">
          <span>12px</span>
          <span>24px</span>
          <span>36px</span>
          <span>48px</span>
        </div>
      </div>

      <!-- Font Weight -->
      <div class="form-control">
        <label class="label">
          <span class="label-text font-medium">Font Weight</span>
        </label>
        <select
          v-model="localStyles.fontWeight"
          class="select select-bordered w-full"
          @change="updateStyles"
        >
          <option value="300">Light (300)</option>
          <option value="400">Normal (400)</option>
          <option value="500">Medium (500)</option>
          <option value="600">Semi Bold (600)</option>
          <option value="700">Bold (700)</option>
          <option value="800">Extra Bold (800)</option>
          <option value="900">Black (900)</option>
        </select>
      </div>

      <!-- Font Style -->
      <div class="form-control">
        <label class="label">
          <span class="label-text font-medium">Font Style</span>
        </label>
        <div class="flex gap-2">
          <label class="label cursor-pointer">
            <input
              v-model="localStyles.fontStyle"
              type="radio"
              value="normal"
              class="radio radio-primary"
              @change="updateStyles"
            />
            <span class="label-text ml-2">Normal</span>
          </label>
          <label class="label cursor-pointer">
            <input
              v-model="localStyles.fontStyle"
              type="radio"
              value="italic"
              class="radio radio-primary"
              @change="updateStyles"
            />
            <span class="label-text ml-2">Italic</span>
          </label>
        </div>
      </div>

      <!-- Text Color -->
      <div class="form-control">
        <label class="label">
          <span class="label-text font-medium">Text Color</span>
        </label>
        <div class="flex items-center gap-3">
          <div
            class="w-12 h-12 rounded-lg border-2 border-base-300 cursor-pointer flex items-center justify-center"
            :style="{ backgroundColor: localStyles.color }"
            @click="showColorPicker = !showColorPicker"
          >
            <span v-if="!localStyles.color" class="text-xs text-base-content/50"
              >Pick</span
            >
          </div>
          <input
            v-model="localStyles.color"
            type="text"
            placeholder="#000000"
            class="input input-bordered flex-1"
            @input="updateStyles"
          />
        </div>

        <!-- Color Picker -->
        <div v-if="showColorPicker" class="mt-2">
          <ColorPicker
            v-model:pureColor="localStyles.color"
            format="hex"
            @update:pureColor="updateStyles"
          />
        </div>
      </div>

      <!-- Spacing Controls -->
      <div class="divider">Spacing & Layout</div>

      <!-- Margin Controls -->
      <div class="form-control">
        <label class="label">
          <span class="label-text font-medium">Margin</span>
          <button
            type="button"
            @click="toggleMarginLink"
            :class="[
              'btn btn-xs',
              marginLinked ? 'btn-primary' : 'btn-outline',
            ]"
          >
            <Icon
              :name="marginLinked ? 'heroicons:link' : 'heroicons:link-slash'"
              class="h-3 w-3"
            />
          </button>
        </label>
        <div class="grid grid-cols-2 gap-2">
          <div class="form-control">
            <label class="label py-1">
              <span class="label-text-alt">Top</span>
            </label>
            <input
              v-model.number="localStyles.marginTop"
              type="number"
              min="0"
              max="100"
              class="input input-bordered input-sm"
              @input="updateMargin('top', $event.target.value)"
            />
          </div>
          <div class="form-control">
            <label class="label py-1">
              <span class="label-text-alt">Right</span>
            </label>
            <input
              v-model.number="localStyles.marginRight"
              type="number"
              min="0"
              max="100"
              class="input input-bordered input-sm"
              @input="updateMargin('right', $event.target.value)"
            />
          </div>
          <div class="form-control">
            <label class="label py-1">
              <span class="label-text-alt">Bottom</span>
            </label>
            <input
              v-model.number="localStyles.marginBottom"
              type="number"
              min="0"
              max="100"
              class="input input-bordered input-sm"
              @input="updateMargin('bottom', $event.target.value)"
            />
          </div>
          <div class="form-control">
            <label class="label py-1">
              <span class="label-text-alt">Left</span>
            </label>
            <input
              v-model.number="localStyles.marginLeft"
              type="number"
              min="0"
              max="100"
              class="input input-bordered input-sm"
              @input="updateMargin('left', $event.target.value)"
            />
          </div>
        </div>
      </div>

      <!-- Padding Controls -->
      <div class="form-control">
        <label class="label">
          <span class="label-text font-medium">Padding</span>
          <button
            type="button"
            @click="togglePaddingLink"
            :class="[
              'btn btn-xs',
              paddingLinked ? 'btn-primary' : 'btn-outline',
            ]"
          >
            <Icon
              :name="paddingLinked ? 'heroicons:link' : 'heroicons:link-slash'"
              class="h-3 w-3"
            />
          </button>
        </label>
        <div class="grid grid-cols-2 gap-2">
          <div class="form-control">
            <label class="label py-1">
              <span class="label-text-alt">Top</span>
            </label>
            <input
              v-model.number="localStyles.paddingTop"
              type="number"
              min="0"
              max="100"
              class="input input-bordered input-sm"
              @input="updatePadding('top', $event.target.value)"
            />
          </div>
          <div class="form-control">
            <label class="label py-1">
              <span class="label-text-alt">Right</span>
            </label>
            <input
              v-model.number="localStyles.paddingRight"
              type="number"
              min="0"
              max="100"
              class="input input-bordered input-sm"
              @input="updatePadding('right', $event.target.value)"
            />
          </div>
          <div class="form-control">
            <label class="label py-1">
              <span class="label-text-alt">Bottom</span>
            </label>
            <input
              v-model.number="localStyles.paddingBottom"
              type="number"
              min="0"
              max="100"
              class="input input-bordered input-sm"
              @input="updatePadding('bottom', $event.target.value)"
            />
          </div>
          <div class="form-control">
            <label class="label py-1">
              <span class="label-text-alt">Left</span>
            </label>
            <input
              v-model.number="localStyles.paddingLeft"
              type="number"
              min="0"
              max="100"
              class="input input-bordered input-sm"
              @input="updatePadding('left', $event.target.value)"
            />
          </div>
        </div>
      </div>

      <!-- Alignment Controls -->
      <div class="form-control">
        <label class="label">
          <span class="label-text font-medium">Vertical Alignment</span>
        </label>
        <div class="flex gap-2">
          <label class="label cursor-pointer">
            <input
              v-model="localStyles.verticalAlign"
              type="radio"
              value="top"
              class="radio radio-primary"
              @change="updateStyles"
            />
            <span class="label-text ml-2">Top</span>
          </label>
          <label class="label cursor-pointer">
            <input
              v-model="localStyles.verticalAlign"
              type="radio"
              value="center"
              class="radio radio-primary"
              @change="updateStyles"
            />
            <span class="label-text ml-2">Center</span>
          </label>
          <label class="label cursor-pointer">
            <input
              v-model="localStyles.verticalAlign"
              type="radio"
              value="bottom"
              class="radio radio-primary"
              @change="updateStyles"
            />
            <span class="label-text ml-2">Bottom</span>
          </label>
        </div>
      </div>

      <!-- Preview -->
      <div class="form-control">
        <label class="label">
          <span class="label-text font-medium">Preview</span>
        </label>
        <div
          class="p-4 bg-base-200 rounded-lg border-2 border-dashed border-base-300"
        >
          <div
            :class="[
              'flex gap-2',
              localStyles.verticalAlign === 'center' ? 'items-center' : '',
              localStyles.verticalAlign === 'top' ? 'items-start' : '',
              localStyles.verticalAlign === 'bottom' ? 'items-end' : '',
              localStyles.verticalAlign !== 'center' ? 'h-16' : '',
            ]"
          >
            <div
              class="w-8 h-8 bg-primary rounded flex items-center justify-center flex-shrink-0"
            >
              <span class="text-primary-content text-xs">Logo</span>
            </div>
            <span v-if="localText" :style="previewStyle">{{ localText }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ColorPicker } from "vue3-colorpicker";
import "vue3-colorpicker/style.css";

// Props
const props = defineProps({
  text: {
    type: String,
    default: "",
  },
  styles: {
    type: Object,
    default: () => ({
      fontFamily: "Inter",
      fontSize: 20,
      fontWeight: "600",
      fontStyle: "normal",
      color: "#000000",
      marginTop: 0,
      marginRight: 0,
      marginBottom: 0,
      marginLeft: 0,
      paddingTop: 0,
      paddingRight: 0,
      paddingBottom: 0,
      paddingLeft: 0,
      verticalAlign: "center",
    }),
  },
});

// Emits
const emit = defineEmits(["update:text", "update:styles"]);

// Local state
const localText = ref(props.text);
const localStyles = ref({ ...props.styles });
const showColorPicker = ref(false);
const marginLinked = ref(false);
const paddingLinked = ref(false);

// Computed preview styles
const previewStyle = computed(() => ({
  fontFamily: localStyles.value.fontFamily,
  fontSize: `${localStyles.value.fontSize}px`,
  fontWeight: localStyles.value.fontWeight,
  fontStyle: localStyles.value.fontStyle,
  color: localStyles.value.color,
  marginTop: `${localStyles.value.marginTop}px`,
  marginRight: `${localStyles.value.marginRight}px`,
  marginBottom: `${localStyles.value.marginBottom}px`,
  marginLeft: `${localStyles.value.marginLeft}px`,
  paddingTop: `${localStyles.value.paddingTop}px`,
  paddingRight: `${localStyles.value.paddingRight}px`,
  paddingBottom: `${localStyles.value.paddingBottom}px`,
  paddingLeft: `${localStyles.value.paddingLeft}px`,
}));

// Methods
const updateText = () => {
  emit("update:text", localText.value);
};

const updateStyles = () => {
  emit("update:styles", { ...localStyles.value });
};

// Margin and padding methods
const toggleMarginLink = () => {
  marginLinked.value = !marginLinked.value;
};

const togglePaddingLink = () => {
  paddingLinked.value = !paddingLinked.value;
};

const updateMargin = (direction, value) => {
  const numValue = parseInt(value) || 0;

  if (marginLinked.value) {
    localStyles.value.marginTop = numValue;
    localStyles.value.marginRight = numValue;
    localStyles.value.marginBottom = numValue;
    localStyles.value.marginLeft = numValue;
  } else {
    localStyles.value[
      `margin${direction.charAt(0).toUpperCase() + direction.slice(1)}`
    ] = numValue;
  }

  updateStyles();
};

const updatePadding = (direction, value) => {
  const numValue = parseInt(value) || 0;

  if (paddingLinked.value) {
    localStyles.value.paddingTop = numValue;
    localStyles.value.paddingRight = numValue;
    localStyles.value.paddingBottom = numValue;
    localStyles.value.paddingLeft = numValue;
  } else {
    localStyles.value[
      `padding${direction.charAt(0).toUpperCase() + direction.slice(1)}`
    ] = numValue;
  }

  updateStyles();
};

// Watch for prop changes
watch(
  () => props.text,
  (newText) => {
    localText.value = newText;
  }
);

watch(
  () => props.styles,
  (newStyles) => {
    localStyles.value = { ...newStyles };
  },
  { deep: true }
);
</script>
