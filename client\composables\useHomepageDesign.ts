// client/composables/useHomepageDesign.ts
export const useHomepageDesign = () => {
  const contentStore = useContentStore();

  // Generate dynamic CSS from design settings
  const generateDynamicCSS = () => {
    const design = contentStore.homepageDesign;
    if (!design) return "";

    let css = "";

    // Typography CSS - Only apply to public pages, but exclude carousel text
    if (design.typographyDesign) {
      const typography = design.typographyDesign;

      Object.keys(typography).forEach((heading) => {
        const styles = typography[heading];
        // Only apply to elements within public page containers, but exclude carousel text
        css += `
          .public-page ${heading}:not(.carousel-title):not(.carousel-subtitle),
          .homepage-content ${heading}:not(.carousel-title):not(.carousel-subtitle),
          .product-page ${heading}:not(.carousel-title):not(.carousel-subtitle),
          .category-page ${heading}:not(.carousel-title):not(.carousel-subtitle) {
            font-family: '${styles.fontFamily}', sans-serif !important;
            font-size: ${styles.fontSize}px !important;
            font-weight: ${styles.fontWeight} !important;
            color: ${styles.color} !important;
          }
        `;
      });
    }

    // Carousel Text CSS - Override typography for carousel elements
    if (design.heroDesign?.textDesign) {
      const textDesign = design.heroDesign.textDesign;

      // Carousel title styles
      if (textDesign.title) {
        css += `
          .carousel-title {
            font-family: '${textDesign.title.fontFamily}', sans-serif !important;
            font-size: ${textDesign.title.fontSize}px !important;
            font-weight: ${textDesign.title.fontWeight} !important;
            color: ${textDesign.title.color} !important;
          }
        `;
      }

      // Carousel subtitle styles
      if (textDesign.subtitle) {
        css += `
          .carousel-subtitle {
            font-family: '${textDesign.subtitle.fontFamily}', sans-serif !important;
            font-size: ${textDesign.subtitle.fontSize}px !important;
            font-weight: ${textDesign.subtitle.fontWeight} !important;
            color: ${textDesign.subtitle.color} !important;
          }
        `;
      }
    }

    // Button CSS
    if (design.buttonDesign) {
      const buttons = design.buttonDesign;

      Object.keys(buttons).forEach((buttonType) => {
        const styles = buttons[buttonType];
        const className =
          buttonType === "primary" ? ".btn-primary" : ".btn-secondary";

        css += `
          .public-page ${className}:not(.carousel-button),
          .homepage-content ${className}:not(.carousel-button),
          .product-page ${className}:not(.carousel-button),
          .category-page ${className}:not(.carousel-button) {
            background-color: ${styles.colors.bg} !important;
            color: ${styles.colors.text} !important;
            border-color: ${styles.colors.border} !important;
            transition: all 0.3s ease !important;
          }

          .public-page ${className}:not(.carousel-button):hover,
          .homepage-content ${className}:not(.carousel-button):hover,
          .product-page ${className}:not(.carousel-button):hover,
          .category-page ${className}:not(.carousel-button):hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
          }

          .public-page ${className}:active,
          .homepage-content ${className}:active,
          .product-page ${className}:active,
          .category-page ${className}:active {
            transform: translateY(0) !important;
          }
        `;

        // Size classes
        if (styles.size !== "default") {
          css += `
            .public-page ${className},
            .homepage-content ${className},
            .product-page ${className},
            .category-page ${className} {
              ${getSizeCSS(styles.size)}
            }
          `;
        }

        // Style classes
        if (styles.style === "square") {
          css += `
            .public-page ${className},
            .homepage-content ${className},
            .product-page ${className},
            .category-page ${className} {
              border-radius: 0 !important;
            }
          `;
        } else if (styles.style === "circle") {
          css += `
            .public-page ${className},
            .homepage-content ${className},
            .product-page ${className},
            .category-page ${className} {
              border-radius: 50% !important;
            }
          `;
        }
      });
    }

    // Carousel Button CSS - Override general button styles for carousel buttons
    if (design.heroDesign?.buttonDesign) {
      const buttonDesign = design.heroDesign.buttonDesign;

      if (buttonDesign.style === "custom") {
        css += `
          .carousel-button {
            background-color: ${buttonDesign.backgroundColor} !important;
            color: ${buttonDesign.textColor} !important;
            border-color: ${buttonDesign.borderColor} !important;
          }
        `;
      }
    }

    // Cards CSS - Only apply to public pages
    if (design.cardsDesign) {
      const cards = design.cardsDesign;
      css += `
        .public-page .card,
        .homepage-content .card,
        .product-page .card,
        .category-page .card {
          background-color: ${cards.backgroundColor} !important;
          border-radius: ${getBorderRadiusValue(cards.borderRadius)} !important;
          box-shadow: ${getShadowValue(cards.shadow)} !important;
          transition: all 0.3s ease !important;
        }

        .public-page .card:hover,
        .homepage-content .card:hover,
        .product-page .card:hover,
        .category-page .card:hover {
          transform: translateY(-4px) !important;
          box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1) !important;
        }

        .public-page .card-body,
        .homepage-content .card-body,
        .product-page .card-body,
        .category-page .card-body {
          padding: ${getPaddingValue(cards.padding)} !important;
        }
      `;
    }

    // Page background CSS
    if (design.pageDesign) {
      const page = design.pageDesign;

      if (page.backgroundType === "color") {
        css += `
          .public-page,
          .homepage-content,
          .product-page,
          .category-page {
            background-color: ${page.backgroundColor} !important;
          }
        `;
      } else if (page.backgroundType === "gradient") {
        const direction = page.gradient.direction;
        // Convert direction to proper CSS linear-gradient syntax
        const cssDirection = direction
          .replace("to-r", "to right")
          .replace("to-l", "to left")
          .replace("to-b", "to bottom")
          .replace("to-t", "to top")
          .replace("to-br", "to bottom right")
          .replace("to-bl", "to bottom left");

        css += `
          .public-page,
          .homepage-content,
          .product-page,
          .category-page {
            background: linear-gradient(${cssDirection}, ${page.gradient.from}, ${page.gradient.to}) !important;
          }
        `;
      } else if (page.backgroundType === "image" && page.backgroundImage) {
        css += `
          .public-page,
          .homepage-content,
          .product-page,
          .category-page {
            background-image: url('${page.backgroundImage}') !important;
            background-size: cover !important;
            background-position: center !important;
            background-attachment: fixed !important;
          }
        `;

        // Apply overlay if specified
        if (page.overlay.type === "color") {
          css += `
            .public-page::before,
            .homepage-content::before,
            .product-page::before,
            .category-page::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background-color: ${page.overlay.color};
              opacity: ${page.overlay.opacity};
              pointer-events: none;
              z-index: -1;
            }
          `;
        } else if (page.overlay.type === "grayscale") {
          css += `
            .public-page,
            .homepage-content,
            .product-page,
            .category-page {
              filter: grayscale(${page.overlay.opacity * 100}%) !important;
            }
          `;
        }
      }
    }

    return css;
  };

  // Helper functions
  const getSizeCSS = (size: string) => {
    switch (size) {
      case "xs":
        return "font-size: 0.75rem; padding: 0.25rem 0.5rem;";
      case "sm":
        return "font-size: 0.875rem; padding: 0.375rem 0.75rem;";
      case "lg":
        return "font-size: 1.125rem; padding: 0.75rem 1.5rem;";
      case "xl":
        return "font-size: 1.25rem; padding: 1rem 2rem;";
      default:
        return "";
    }
  };

  const getBorderRadiusValue = (radius: string) => {
    const radiusMap: Record<string, string> = {
      "rounded-none": "0",
      "rounded-sm": "0.125rem",
      rounded: "0.25rem",
      "rounded-lg": "0.5rem",
      "rounded-xl": "0.75rem",
      "rounded-2xl": "1rem",
      "rounded-full": "9999px",
    };
    return radiusMap[radius] || "0.5rem";
  };

  const getShadowValue = (shadow: string) => {
    const shadowMap: Record<string, string> = {
      "shadow-none": "none",
      "shadow-sm": "0 1px 2px 0 rgb(0 0 0 / 0.05)",
      shadow: "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",
      "shadow-md":
        "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
      "shadow-lg":
        "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",
      "shadow-xl":
        "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)",
      "shadow-2xl": "0 25px 50px -12px rgb(0 0 0 / 0.25)",
    };
    return shadowMap[shadow] || shadowMap["shadow-xl"];
  };

  const getPaddingValue = (padding: string) => {
    const paddingMap: Record<string, string> = {
      "p-2": "0.5rem",
      "p-4": "1rem",
      "p-6": "1.5rem",
      "p-8": "2rem",
    };
    return paddingMap[padding] || "1.5rem";
  };

  // Apply dynamic CSS to the page (scoped to public page classes)
  const applyDynamicCSS = () => {
    const css = generateDynamicCSS();

    // Remove existing dynamic styles
    const existingStyle = document.getElementById("homepage-dynamic-styles");
    if (existingStyle) {
      existingStyle.remove();
    }

    // Add new dynamic styles
    if (css) {
      const styleElement = document.createElement("style");
      styleElement.id = "homepage-dynamic-styles";
      styleElement.textContent = css;
      document.head.appendChild(styleElement);
    }
  };

  // Watch for design changes and apply CSS
  const initializeDynamicStyling = () => {
    // Apply initial styles
    nextTick(() => {
      applyDynamicCSS();
    });

    // Watch for changes
    watch(
      () => contentStore.homepageDesign,
      () => {
        nextTick(() => {
          applyDynamicCSS();
        });
      },
      { deep: true }
    );
  };

  return {
    generateDynamicCSS,
    applyDynamicCSS,
    initializeDynamicStyling,
  };
};
