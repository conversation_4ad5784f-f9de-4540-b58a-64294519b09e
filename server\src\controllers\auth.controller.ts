import { Request, Response, NextFunction } from "express";
import { PrismaClient } from "@prisma/client";
import { generateToken, hashPassword, comparePassword } from "../utils/auth";
import { createError } from "../utils/wrapController";

const prisma = new PrismaClient();

// Request interfaces
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

/**
 * POST /api/auth/login
 * User login
 */
export const login = async (
  req: Request<{}, {}, LoginRequest>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const { email, password } = req.body;

  try {
    // Validation
    if (!email || !password) {
      next(createError(400, "Email and password are required"));
      return;
    }

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
    });

    if (!user) {
      next(createError(401, "Invalid email or password"));
      return;
    }

    // Check password
    const isPasswordValid = await comparePassword(password, user.password);
    if (!isPasswordValid) {
      next(createError(401, "Invalid email or password"));
      return;
    }

    // Generate token
    const token = generateToken({
      userId: user.id,
      email: user.email,
      role: user.role,
    });

    // Return user data (without password) and token
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      message: "Login successful",
      user: userWithoutPassword,
      token,
    });
  } catch (error) {
    console.error("Login error:", error);
    next(createError(500, "Login failed"));
  }
};

/**
 * POST /api/auth/register
 * User registration
 */
export const register = async (
  req: Request<{}, {}, RegisterRequest>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const { email, password, firstName, lastName } = req.body;

  try {
    // Validation
    if (!email || !password) {
      next(createError(400, "Email and password are required"));
      return;
    }

    if (password.length < 6) {
      next(createError(400, "Password must be at least 6 characters long"));
      return;
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
    });

    if (existingUser) {
      next(createError(409, "User with this email already exists"));
      return;
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create user
    const user = await prisma.user.create({
      data: {
        email: email.toLowerCase(),
        password: hashedPassword,
        firstName,
        lastName,
        role: "USER",
      },
    });

    // Generate token
    const token = generateToken({
      userId: user.id,
      email: user.email,
      role: user.role,
    });

    // Return user data (without password) and token
    const { password: _, ...userWithoutPassword } = user;

    res.status(201).json({
      message: "Registration successful",
      user: userWithoutPassword,
      token,
    });
  } catch (error) {
    console.error("Registration error:", error);
    next(createError(500, "Registration failed"));
  }
};

/**
 * GET /api/auth/me
 * Get current user profile
 */
export const getProfile = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user!.id },
      select: {
        id: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      next(createError(404, "User not found"));
      return;
    }

    res.json({ user });
  } catch (error) {
    console.error("Get profile error:", error);
    next(createError(500, "Failed to get user profile"));
  }
};

/**
 * PUT /api/auth/profile
 * Update user profile
 */
export const updateProfile = async (
  req: Request<{}, {}, UpdateProfileRequest>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const { firstName, lastName } = req.body;

  try {
    const updatedUser = await prisma.user.update({
      where: { id: req.user!.id },
      data: {
        firstName,
        lastName,
      },
      select: {
        id: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    res.json({
      message: "Profile updated successfully",
      user: updatedUser,
    });
  } catch (error) {
    console.error("Update profile error:", error);
    next(createError(500, "Failed to update profile"));
  }
};

/**
 * POST /api/auth/change-password
 * Change user password
 */
export const changePassword = async (
  req: Request<{}, {}, ChangePasswordRequest>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const { currentPassword, newPassword } = req.body;

  try {
    // Validation
    if (!currentPassword || !newPassword) {
      next(createError(400, "Current password and new password are required"));
      return;
    }

    if (newPassword.length < 6) {
      next(createError(400, "New password must be at least 6 characters long"));
      return;
    }

    // Get user with password
    const user = await prisma.user.findUnique({
      where: { id: req.user!.id },
    });

    if (!user) {
      next(createError(404, "User not found"));
      return;
    }

    // Verify current password
    const isCurrentPasswordValid = await comparePassword(
      currentPassword,
      user.password
    );
    if (!isCurrentPasswordValid) {
      next(createError(401, "Current password is incorrect"));
      return;
    }

    // Hash new password
    const hashedNewPassword = await hashPassword(newPassword);

    // Update password
    await prisma.user.update({
      where: { id: req.user!.id },
      data: { password: hashedNewPassword },
    });

    res.json({
      message: "Password changed successfully",
    });
  } catch (error) {
    console.error("Change password error:", error);
    next(createError(500, "Failed to change password"));
  }
};
