<template>
  <div class="space-y-6">
    <!-- Section Header -->
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold">Benefit Cards Design</h3>
      <div class="flex gap-2">
        <button
          @click="addCard"
          :disabled="localDesign.cards.length >= 6"
          class="btn btn-sm btn-primary"
        >
          <Icon name="heroicons:plus" class="h-4 w-4" />
          Add Card
        </button>
        <button @click="resetToDefaults" class="btn btn-sm btn-outline">
          Reset to Defaults
        </button>
      </div>
    </div>

    <!-- Section Background & Padding -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-medium mb-3">Section Styling</h4>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Background Color -->
        <div class="form-control">
          <label class="label">
            <span class="label-text">Background Color</span>
          </label>
          <input
            type="color"
            v-model="localDesign.backgroundColor"
            class="input input-bordered h-12"
            @input="updateDesign"
          />
        </div>

        <!-- Padding Controls -->
        <div class="form-control">
          <label class="label">
            <span class="label-text">Section Padding</span>
          </label>
          <div class="grid grid-cols-2 gap-2">
            <div>
              <label class="label-text text-xs">Top/Bottom</label>
              <input
                type="range"
                min="0"
                max="100"
                v-model="localDesign.padding.vertical"
                class="range range-sm"
                @input="updateDesign"
              />
              <span class="text-xs">{{ localDesign.padding.vertical }}px</span>
            </div>
            <div>
              <label class="label-text text-xs">Left/Right</label>
              <input
                type="range"
                min="0"
                max="100"
                v-model="localDesign.padding.horizontal"
                class="range range-sm"
                @input="updateDesign"
              />
              <span class="text-xs">{{ localDesign.padding.horizontal }}px</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Cards List -->
    <div class="space-y-4">
      <h4 class="font-medium">Cards ({{ localDesign.cards.length }}/6)</h4>
      
      <div
        v-for="(card, index) in localDesign.cards"
        :key="card.id"
        class="card bg-base-100 border border-base-300 p-4"
      >
        <div class="flex items-start justify-between mb-3">
          <h5 class="font-medium">Card {{ index + 1 }}</h5>
          <button
            @click="removeCard(index)"
            class="btn btn-sm btn-ghost btn-circle text-error"
          >
            <Icon name="heroicons:x-mark" class="h-4 w-4" />
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Icon/Image Section -->
          <div class="space-y-3">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Icon Type</span>
              </label>
              <select
                v-model="card.iconType"
                class="select select-bordered select-sm"
                @change="updateDesign"
              >
                <option value="emoji">Emoji Icon</option>
                <option value="image">Upload Image</option>
              </select>
            </div>

            <!-- Emoji Icon Input -->
            <div v-if="card.iconType === 'emoji'" class="form-control">
              <label class="label">
                <span class="label-text">Emoji Icon</span>
              </label>
              <input
                v-model="card.icon"
                type="text"
                placeholder="🚚"
                class="input input-bordered input-sm"
                @input="updateDesign"
              />
            </div>

            <!-- Image Upload -->
            <div v-else class="form-control">
              <label class="label">
                <span class="label-text">Upload Icon Image</span>
              </label>
              <div class="flex gap-2">
                <input
                  type="file"
                  accept="image/*"
                  @change="handleImageUpload($event, index)"
                  class="file-input file-input-bordered file-input-sm flex-1"
                />
                <button
                  v-if="card.image"
                  @click="removeImage(index)"
                  class="btn btn-sm btn-outline btn-error"
                >
                  Remove
                </button>
              </div>
              <!-- Image Preview -->
              <div v-if="card.image" class="mt-2">
                <NuxtImg
                  :src="card.image"
                  :alt="card.title"
                  class="w-16 h-16 object-cover rounded"
                />
              </div>
            </div>
          </div>

          <!-- Content Section -->
          <div class="space-y-3">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Title</span>
              </label>
              <input
                v-model="card.title"
                type="text"
                placeholder="Free Shipping"
                class="input input-bordered input-sm"
                @input="updateDesign"
              />
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text">Description</span>
              </label>
              <textarea
                v-model="card.description"
                placeholder="Free shipping on orders over $50"
                class="textarea textarea-bordered textarea-sm"
                rows="3"
                @input="updateDesign"
              ></textarea>
            </div>
          </div>
        </div>
      </div>

      <!-- Add Card Button (when less than 6) -->
      <div v-if="localDesign.cards.length < 6" class="text-center">
        <button @click="addCard" class="btn btn-outline btn-wide">
          <Icon name="heroicons:plus" class="h-4 w-4" />
          Add Another Card
        </button>
      </div>
    </div>

    <!-- Preview Section -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-medium mb-3">Preview</h4>
      <div
        class="rounded-lg p-6"
        :style="{
          backgroundColor: localDesign.backgroundColor,
          paddingTop: localDesign.padding.vertical + 'px',
          paddingBottom: localDesign.padding.vertical + 'px',
          paddingLeft: localDesign.padding.horizontal + 'px',
          paddingRight: localDesign.padding.horizontal + 'px',
        }"
      >
        <BenefitCardsPreview :design="localDesign" />
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  design: {
    type: Object,
    default: () => getDefaultDesign(),
  },
});

// Emits
const emit = defineEmits(["update:design"]);

// Local design state
const localDesign = ref({ ...props.design });

// Watch for prop changes
watch(
  () => props.design,
  (newDesign) => {
    localDesign.value = { ...newDesign };
  },
  { deep: true }
);

// Default design settings
const getDefaultDesign = () => ({
  backgroundColor: "#f8fafc",
  padding: {
    vertical: 60,
    horizontal: 20,
  },
  cards: [
    {
      id: "default-1",
      title: "Free Shipping",
      description: "Free shipping on orders over $50",
      icon: "🚚",
      iconType: "emoji",
      image: null,
    },
    {
      id: "default-2",
      title: "24/7 Support",
      description: "Round-the-clock customer support",
      icon: "💬",
      iconType: "emoji",
      image: null,
    },
    {
      id: "default-3",
      title: "Easy Returns",
      description: "30-day hassle-free returns",
      icon: "↩️",
      iconType: "emoji",
      image: null,
    },
    {
      id: "default-4",
      title: "Secure Payment",
      description: "Your payment information is safe",
      icon: "🔒",
      iconType: "emoji",
      image: null,
    },
  ],
});

// Update design
const updateDesign = () => {
  emit("update:design", { ...localDesign.value });
};

// Add new card
const addCard = () => {
  if (localDesign.value.cards.length >= 6) return;
  
  localDesign.value.cards.push({
    id: Date.now() + Math.random(),
    title: "",
    description: "",
    icon: "✨",
    iconType: "emoji",
    image: null,
  });
  updateDesign();
};

// Remove card
const removeCard = (index) => {
  localDesign.value.cards.splice(index, 1);
  updateDesign();
};

// Reset to defaults
const resetToDefaults = () => {
  localDesign.value = getDefaultDesign();
  updateDesign();
};

// Handle image upload
const handleImageUpload = async (event, cardIndex) => {
  const file = event.target.files[0];
  if (!file) return;

  try {
    const api = useApi();
    const formData = new FormData();
    formData.append("image", file);

    const response = await api.upload(
      "/admin/content/upload-benefit-image",
      formData,
      {
        showLoading: true,
        loadingMessage: "Uploading image...",
      }
    );

    localDesign.value.cards[cardIndex].image = response.imageUrl;
    localDesign.value.cards[cardIndex].iconType = "image";
    updateDesign();
  } catch (error) {
    console.error("Error uploading image:", error);
    const { $toast } = useNuxtApp();
    $toast.error(`Failed to upload image: ${file.name}`);
  }
};

// Remove image
const removeImage = (cardIndex) => {
  localDesign.value.cards[cardIndex].image = null;
  localDesign.value.cards[cardIndex].iconType = "emoji";
  updateDesign();
};

// Initialize with default design if empty
onMounted(() => {
  if (!localDesign.value.cards || localDesign.value.cards.length === 0) {
    localDesign.value = getDefaultDesign();
    updateDesign();
  }
});
</script>
