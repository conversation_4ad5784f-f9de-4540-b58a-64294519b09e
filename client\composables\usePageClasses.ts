// client/composables/usePageClasses.ts
export const usePageClasses = () => {
  const route = useRoute();

  // Get page-specific classes based on current route
  const getPageClasses = computed(() => {
    const classes = ['public-page']; // Always include public-page for non-admin routes
    
    // Don't add public page classes to admin routes
    if (route.path.startsWith('/admin')) {
      return [];
    }
    
    // Add specific classes based on route
    if (route.path === '/') {
      classes.push('homepage-content');
    } else if (route.path.startsWith('/products/')) {
      classes.push('product-page');
    } else if (route.path.startsWith('/categories/')) {
      classes.push('category-page');
    } else if (route.path.startsWith('/products')) {
      classes.push('products-page');
    } else {
      // Default for other public pages
      classes.push('public-content');
    }
    
    return classes;
  });

  // Get main content classes
  const getMainClasses = computed(() => {
    if (route.path.startsWith('/admin')) {
      return 'admin-content'; // Admin pages get different class
    }
    
    if (route.path === '/') {
      return 'homepage-content';
    } else if (route.path.startsWith('/products/')) {
      return 'product-page';
    } else if (route.path.startsWith('/categories/')) {
      return 'category-page';
    } else {
      return 'public-content';
    }
  });

  return {
    getPageClasses,
    getMainClasses
  };
};
