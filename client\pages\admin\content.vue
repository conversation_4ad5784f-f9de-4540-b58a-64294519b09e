<template>
  <div>
    <!-- <PERSON> Header -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold">Homepage Design</h1>
        <p class="text-base-content/70 mt-2">
          Customize your homepage appearance and layout
        </p>
      </div>

      <!-- Action Buttons -->
      <div class="flex gap-3">
        <NuxtLink to="/admin" class="btn btn-outline">
          <Icon name="heroicons:arrow-left" class="h-4 w-4" />
          Back to Admin
        </NuxtLink>
        <button @click="previewChanges" class="btn btn-secondary">
          <Icon name="heroicons:eye" class="h-4 w-4" />
          Preview
        </button>
        <button
          @click="publishChanges"
          class="btn btn-primary"
          :disabled="!hasChanges"
        >
          <Icon name="heroicons:rocket-launch" class="h-4 w-4" />
          Publish Changes
        </button>
      </div>
    </div>

    <!-- Design Interface -->
    <div class="flex gap-6 min-h-screen">
      <!-- Left Sidebar - Design Sections -->
      <div class="w-80 bg-base-100 rounded-lg shadow-sm border p-6">
        <h2 class="text-xl font-semibold mb-6">Design Sections</h2>

        <div class="space-y-2">
          <button
            v-for="section in designSections"
            :key="section.id"
            @click="activeSection = section.id"
            :class="[
              'btn btn-ghost w-full justify-start',
              activeSection === section.id ? 'btn-active' : '',
            ]"
          >
            <Icon :name="section.icon" class="h-4 w-4" />
            {{ section.name }}
          </button>
        </div>

        <!-- Quick Actions -->
        <div class="divider"></div>
        <div class="space-y-2">
          <button
            @click="resetToDefaults"
            class="btn btn-outline btn-warning btn-sm w-full"
          >
            <Icon name="heroicons:arrow-path" class="h-4 w-4" />
            Reset to Defaults
          </button>
          <button
            @click="exportSettings"
            class="btn btn-outline btn-info btn-sm w-full"
          >
            <Icon name="heroicons:arrow-down-tray" class="h-4 w-4" />
            Export Settings
          </button>
        </div>
      </div>

      <!-- Main Content Area -->
      <div class="flex-1">
        <!-- Navbar Design -->
        <div v-if="activeSection === 'navbar'" class="space-y-6">
          <NavbarDesigner
            v-model:design="designSettings.navbarDesign"
            @update="markAsChanged"
          />
        </div>

        <!-- Hero Section Design -->
        <div v-else-if="activeSection === 'hero'" class="space-y-6">
          <HeroDesigner
            v-model:design="designSettings.heroDesign"
            @update="markAsChanged"
          />
        </div>

        <!-- Cards Design -->
        <div v-else-if="activeSection === 'cards'" class="space-y-6">
          <CardsDesigner
            v-model:design="designSettings.cardsDesign"
            @update="markAsChanged"
          />
        </div>

        <!-- Typography Design -->
        <div v-else-if="activeSection === 'typography'" class="space-y-6">
          <TypographyDesigner
            v-model:design="designSettings.typographyDesign"
            @update="markAsChanged"
          />
        </div>

        <!-- Page Design -->
        <div v-else-if="activeSection === 'page'" class="space-y-6">
          <PageDesigner
            v-model:design="designSettings.pageDesign"
            @update="markAsChanged"
          />
        </div>

        <!-- Button Design -->
        <div v-else-if="activeSection === 'buttons'" class="space-y-6">
          <ButtonDesigner
            v-model:design="designSettings.buttonDesign"
            @update="markAsChanged"
          />
        </div>

        <!-- Default/Welcome Screen -->
        <div v-else class="text-center py-20">
          <Icon
            name="heroicons:paint-brush"
            class="h-16 w-16 mx-auto text-base-content/30 mb-4"
          />
          <h3 class="text-2xl font-semibold mb-2">
            Welcome to Homepage Designer
          </h3>
          <p class="text-base-content/70 mb-6">
            Select a section from the sidebar to start customizing your homepage
          </p>
          <button @click="activeSection = 'navbar'" class="btn btn-primary">
            Start with Navbar Design
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Page metadata
definePageMeta({
  layout: "admin",
  middleware: "admin",
});

// Reactive state
const activeSection = ref("");
const hasChanges = ref(false);

// Design sections configuration
const designSections = [
  { id: "navbar", name: "Navigation Bar", icon: "heroicons:bars-3" },
  { id: "hero", name: "Hero Section", icon: "heroicons:photo" },
  { id: "cards", name: "Cards Design", icon: "heroicons:rectangle-stack" },
  { id: "typography", name: "Typography", icon: "heroicons:document-text" },
  { id: "page", name: "Page Background", icon: "heroicons:swatch" },
  { id: "buttons", name: "Buttons", icon: "heroicons:cursor-arrow-rays" },
];

// Default design settings
const getDefaultSettings = () => ({
  navbarDesign: {
    width: "full",
    alignment: "center",
    height: "default",
    padding: { top: 0, right: 0, bottom: 0, left: 0 },
    margin: { top: 0, right: 0, bottom: 0, left: 0 },
    itemSpacing: "normal",
    linkStyle: "default",
    hoverStyle: "default",
    activeStyle: "default",
    textStyles: {
      fontFamily: "Inter",
      fontSize: 16,
      fontWeight: "500",
      color: "#000000",
    },
  },
  heroDesign: {
    width: "full",
    height: "500px",
    padding: { top: 0, right: 0, bottom: 0, left: 0 },
    margin: { top: 0, right: 0, bottom: 0, left: 0 },
    background: {
      type: "color",
      color: "#ffffff",
      gradient: { from: "#ffffff", to: "#f3f4f6", direction: "to-r" },
    },
    slides: [],
    imagePosition: "cover",
    textDesign: {
      title: {
        fontFamily: "Inter",
        fontSize: 48,
        fontWeight: "700",
        color: "#ffffff",
      },
      subtitle: {
        fontFamily: "Inter",
        fontSize: 18,
        fontWeight: "400",
        color: "#ffffff",
      },
    },
    buttonDesign: {
      style: "primary",
      size: "lg",
      shape: "default",
      backgroundColor: "#3b82f6",
      textColor: "#ffffff",
      borderColor: "#3b82f6",
    },
  },
  cardsDesign: {
    borderRadius: "rounded-lg",
    shadow: "shadow-xl",
    backgroundColor: "#ffffff",
    padding: "p-6",
  },
  typographyDesign: {
    h1: {
      fontFamily: "Inter",
      fontSize: 48,
      fontWeight: "700",
      color: "#000000",
    },
    h2: {
      fontFamily: "Inter",
      fontSize: 36,
      fontWeight: "600",
      color: "#000000",
    },
    h3: {
      fontFamily: "Inter",
      fontSize: 24,
      fontWeight: "600",
      color: "#000000",
    },
    h4: {
      fontFamily: "Inter",
      fontSize: 20,
      fontWeight: "500",
      color: "#000000",
    },
  },
  pageDesign: {
    backgroundColor: "#ffffff",
    backgroundType: "color",
    gradient: { from: "#ffffff", to: "#f3f4f6", direction: "to-b" },
    backgroundImage: null,
    overlay: { type: "none", color: "#000000", opacity: 0.5 },
  },
  buttonDesign: {
    primary: {
      style: "rounded",
      size: "default",
      colors: { bg: "#3b82f6", text: "#ffffff", border: "#3b82f6" },
    },
    secondary: {
      style: "rounded",
      size: "default",
      colors: { bg: "#6b7280", text: "#ffffff", border: "#6b7280" },
    },
  },
});

// Design settings with defaults
const designSettings = reactive(getDefaultSettings());

// Methods
const markAsChanged = () => {
  hasChanges.value = true;
};

const previewChanges = async () => {
  try {
    // First publish the changes temporarily
    const api = useApi();
    await api.put("/admin/content/homepage-design", designSettings);

    // Update the content store
    const contentStore = useContentStore();
    await contentStore.updateHomepageDesign(designSettings);

    // Open preview in new tab with cache busting
    const timestamp = Date.now();
    window.open(`/?preview=${timestamp}`, "_blank");
  } catch (error) {
    console.error("Error preparing preview:", error);
    // Fallback to simple preview
    window.open("/", "_blank");
  }
};

const publishChanges = async () => {
  try {
    // Save all design settings to database
    const api = useApi();
    await api.put("/admin/content/homepage-design", designSettings);

    // Update the content store with new design settings
    const contentStore = useContentStore();
    await contentStore.updateHomepageDesign(designSettings);

    hasChanges.value = false;

    // Show success message
    // You can add a toast notification here
    console.log("Changes published successfully!");
  } catch (error) {
    console.error("Error publishing changes:", error);
  }
};

const resetToDefaults = () => {
  if (
    confirm(
      "Are you sure you want to reset all settings to defaults? This cannot be undone."
    )
  ) {
    // Reset to default values
    Object.assign(designSettings, getDefaultSettings());
    markAsChanged();
  }
};

const exportSettings = () => {
  const dataStr = JSON.stringify(designSettings, null, 2);
  const dataBlob = new Blob([dataStr], { type: "application/json" });
  const url = URL.createObjectURL(dataBlob);
  const link = document.createElement("a");
  link.href = url;
  link.download = "homepage-design-settings.json";
  link.click();
  URL.revokeObjectURL(url);
};

// getDefaultSettings function is defined above

// Load existing settings on mount
onMounted(async () => {
  try {
    const api = useApi();
    const response = await api.get("/admin/content/homepage-design");
    if (response.settings) {
      // Merge loaded settings with defaults to ensure all properties exist
      const defaults = getDefaultSettings();
      Object.keys(defaults).forEach((key) => {
        if (response.settings[key]) {
          designSettings[key] = { ...defaults[key], ...response.settings[key] };
        }
      });
    }
  } catch (error) {
    console.error("Error loading design settings:", error);
  }
});
</script>
