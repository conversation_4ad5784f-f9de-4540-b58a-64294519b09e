
import type { DefineComponent, SlotsType } from 'vue'
type IslandComponent<T extends DefineComponent> = T & DefineComponent<{}, {refresh: () => Promise<void>}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, SlotsType<{ fallback: { error: unknown } }>>
type HydrationStrategies = {
  hydrateOnVisible?: IntersectionObserverInit | true
  hydrateOnIdle?: number | true
  hydrateOnInteraction?: keyof HTMLElementEventMap | Array<keyof HTMLElementEventMap> | true
  hydrateOnMediaQuery?: string
  hydrateAfter?: number
  hydrateWhen?: boolean
  hydrateNever?: true
}
type LazyComponent<T> = (T & DefineComponent<HydrationStrategies, {}, {}, {}, {}, {}, {}, { hydrated: () => void }>)
interface _GlobalComponents {
      'AppFooter': typeof import("../components/AppFooter.vue")['default']
    'AppNavbar': typeof import("../components/AppNavbar.vue")['default']
    'BenefitCards': typeof import("../components/BenefitCards.vue")['default']
    'ButtonDesigner': typeof import("../components/ButtonDesigner.vue")['default']
    'CardsDesigner': typeof import("../components/CardsDesigner.vue")['default']
    'CartDrawer': typeof import("../components/CartDrawer.vue")['default']
    'CategoryGrid': typeof import("../components/CategoryGrid.vue")['default']
    'HeroCarousel': typeof import("../components/HeroCarousel.vue")['default']
    'HeroDesigner': typeof import("../components/HeroDesigner.vue")['default']
    'HeroPreview': typeof import("../components/HeroPreview.vue")['default']
    'LimitedTimeOffer': typeof import("../components/LimitedTimeOffer.vue")['default']
    'LogoImageStyleEditor': typeof import("../components/LogoImageStyleEditor.vue")['default']
    'NavbarDesigner': typeof import("../components/NavbarDesigner.vue")['default']
    'NavbarPreview': typeof import("../components/NavbarPreview.vue")['default']
    'NewArrivals': typeof import("../components/NewArrivals.vue")['default']
    'NewsletterSignup': typeof import("../components/NewsletterSignup.vue")['default']
    'PageDesigner': typeof import("../components/PageDesigner.vue")['default']
    'TextStyleEditor': typeof import("../components/TextStyleEditor.vue")['default']
    'TrendingProducts': typeof import("../components/TrendingProducts.vue")['default']
    'TypographyDesigner': typeof import("../components/TypographyDesigner.vue")['default']
    'NuxtWelcome': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/welcome.vue")['default']
    'NuxtLayout': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'NuxtErrorBoundary': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']
    'ClientOnly': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/client-only")['default']
    'DevOnly': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/dev-only")['default']
    'ServerPlaceholder': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'NuxtLink': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'NuxtLoadingIndicator': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'NuxtTime': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']
    'NuxtRouteAnnouncer': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
    'NuxtImg': typeof import("../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3.2_ioredis@5.6.1_magicast@0.3.5/node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']
    'NuxtPicture': typeof import("../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3.2_ioredis@5.6.1_magicast@0.3.5/node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']
    'Icon': typeof import("../node_modules/.pnpm/@nuxt+icon@1.15.0_magicast@0.3.5_vite@6.3.5_@types+node@24.0.13_jiti@2.4.2_terser@5.43.1_yaml_wewk6rl4tdmtvypb2ndrpw4rxe/node_modules/@nuxt/icon/dist/runtime/components/index")['default']
    'NuxtPage': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/pages/runtime/page")['default']
    'NoScript': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'Link': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Link']
    'Base': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Base']
    'Title': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Title']
    'Meta': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Meta']
    'Style': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Style']
    'Head': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Head']
    'Html': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Html']
    'Body': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Body']
    'NuxtIsland': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-island")['default']
    'NuxtRouteAnnouncer': typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/server-placeholder")['default']
      'LazyAppFooter': LazyComponent<typeof import("../components/AppFooter.vue")['default']>
    'LazyAppNavbar': LazyComponent<typeof import("../components/AppNavbar.vue")['default']>
    'LazyBenefitCards': LazyComponent<typeof import("../components/BenefitCards.vue")['default']>
    'LazyButtonDesigner': LazyComponent<typeof import("../components/ButtonDesigner.vue")['default']>
    'LazyCardsDesigner': LazyComponent<typeof import("../components/CardsDesigner.vue")['default']>
    'LazyCartDrawer': LazyComponent<typeof import("../components/CartDrawer.vue")['default']>
    'LazyCategoryGrid': LazyComponent<typeof import("../components/CategoryGrid.vue")['default']>
    'LazyHeroCarousel': LazyComponent<typeof import("../components/HeroCarousel.vue")['default']>
    'LazyHeroDesigner': LazyComponent<typeof import("../components/HeroDesigner.vue")['default']>
    'LazyHeroPreview': LazyComponent<typeof import("../components/HeroPreview.vue")['default']>
    'LazyLimitedTimeOffer': LazyComponent<typeof import("../components/LimitedTimeOffer.vue")['default']>
    'LazyLogoImageStyleEditor': LazyComponent<typeof import("../components/LogoImageStyleEditor.vue")['default']>
    'LazyNavbarDesigner': LazyComponent<typeof import("../components/NavbarDesigner.vue")['default']>
    'LazyNavbarPreview': LazyComponent<typeof import("../components/NavbarPreview.vue")['default']>
    'LazyNewArrivals': LazyComponent<typeof import("../components/NewArrivals.vue")['default']>
    'LazyNewsletterSignup': LazyComponent<typeof import("../components/NewsletterSignup.vue")['default']>
    'LazyPageDesigner': LazyComponent<typeof import("../components/PageDesigner.vue")['default']>
    'LazyTextStyleEditor': LazyComponent<typeof import("../components/TextStyleEditor.vue")['default']>
    'LazyTrendingProducts': LazyComponent<typeof import("../components/TrendingProducts.vue")['default']>
    'LazyTypographyDesigner': LazyComponent<typeof import("../components/TypographyDesigner.vue")['default']>
    'LazyNuxtWelcome': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/welcome.vue")['default']>
    'LazyNuxtLayout': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
    'LazyNuxtErrorBoundary': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']>
    'LazyClientOnly': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/client-only")['default']>
    'LazyDevOnly': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/dev-only")['default']>
    'LazyServerPlaceholder': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
    'LazyNuxtLink': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-link")['default']>
    'LazyNuxtLoadingIndicator': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
    'LazyNuxtTime': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
    'LazyNuxtImg': LazyComponent<typeof import("../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3.2_ioredis@5.6.1_magicast@0.3.5/node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']>
    'LazyNuxtPicture': LazyComponent<typeof import("../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3.2_ioredis@5.6.1_magicast@0.3.5/node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']>
    'LazyIcon': LazyComponent<typeof import("../node_modules/.pnpm/@nuxt+icon@1.15.0_magicast@0.3.5_vite@6.3.5_@types+node@24.0.13_jiti@2.4.2_terser@5.43.1_yaml_wewk6rl4tdmtvypb2ndrpw4rxe/node_modules/@nuxt/icon/dist/runtime/components/index")['default']>
    'LazyNuxtPage': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/pages/runtime/page")['default']>
    'LazyNoScript': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['NoScript']>
    'LazyLink': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Link']>
    'LazyBase': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Base']>
    'LazyTitle': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Title']>
    'LazyMeta': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Meta']>
    'LazyStyle': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Style']>
    'LazyHead': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Head']>
    'LazyHtml': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Html']>
    'LazyBody': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Body']>
    'LazyNuxtIsland': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-island")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
}

declare module 'vue' {
  export interface GlobalComponents extends _GlobalComponents { }
}

export const AppFooter: typeof import("../components/AppFooter.vue")['default']
export const AppNavbar: typeof import("../components/AppNavbar.vue")['default']
export const BenefitCards: typeof import("../components/BenefitCards.vue")['default']
export const ButtonDesigner: typeof import("../components/ButtonDesigner.vue")['default']
export const CardsDesigner: typeof import("../components/CardsDesigner.vue")['default']
export const CartDrawer: typeof import("../components/CartDrawer.vue")['default']
export const CategoryGrid: typeof import("../components/CategoryGrid.vue")['default']
export const HeroCarousel: typeof import("../components/HeroCarousel.vue")['default']
export const HeroDesigner: typeof import("../components/HeroDesigner.vue")['default']
export const HeroPreview: typeof import("../components/HeroPreview.vue")['default']
export const LimitedTimeOffer: typeof import("../components/LimitedTimeOffer.vue")['default']
export const LogoImageStyleEditor: typeof import("../components/LogoImageStyleEditor.vue")['default']
export const NavbarDesigner: typeof import("../components/NavbarDesigner.vue")['default']
export const NavbarPreview: typeof import("../components/NavbarPreview.vue")['default']
export const NewArrivals: typeof import("../components/NewArrivals.vue")['default']
export const NewsletterSignup: typeof import("../components/NewsletterSignup.vue")['default']
export const PageDesigner: typeof import("../components/PageDesigner.vue")['default']
export const TextStyleEditor: typeof import("../components/TextStyleEditor.vue")['default']
export const TrendingProducts: typeof import("../components/TrendingProducts.vue")['default']
export const TypographyDesigner: typeof import("../components/TypographyDesigner.vue")['default']
export const NuxtWelcome: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/welcome.vue")['default']
export const NuxtLayout: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const NuxtErrorBoundary: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']
export const ClientOnly: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/client-only")['default']
export const DevOnly: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/dev-only")['default']
export const ServerPlaceholder: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const NuxtLink: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const NuxtLoadingIndicator: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const NuxtTime: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']
export const NuxtRouteAnnouncer: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
export const NuxtImg: typeof import("../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3.2_ioredis@5.6.1_magicast@0.3.5/node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']
export const NuxtPicture: typeof import("../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3.2_ioredis@5.6.1_magicast@0.3.5/node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']
export const Icon: typeof import("../node_modules/.pnpm/@nuxt+icon@1.15.0_magicast@0.3.5_vite@6.3.5_@types+node@24.0.13_jiti@2.4.2_terser@5.43.1_yaml_wewk6rl4tdmtvypb2ndrpw4rxe/node_modules/@nuxt/icon/dist/runtime/components/index")['default']
export const NuxtPage: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/pages/runtime/page")['default']
export const NoScript: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const Link: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Link']
export const Base: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Base']
export const Title: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Title']
export const Meta: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Meta']
export const Style: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Style']
export const Head: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Head']
export const Html: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Html']
export const Body: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Body']
export const NuxtIsland: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-island")['default']
export const NuxtRouteAnnouncer: typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const LazyAppFooter: LazyComponent<typeof import("../components/AppFooter.vue")['default']>
export const LazyAppNavbar: LazyComponent<typeof import("../components/AppNavbar.vue")['default']>
export const LazyBenefitCards: LazyComponent<typeof import("../components/BenefitCards.vue")['default']>
export const LazyButtonDesigner: LazyComponent<typeof import("../components/ButtonDesigner.vue")['default']>
export const LazyCardsDesigner: LazyComponent<typeof import("../components/CardsDesigner.vue")['default']>
export const LazyCartDrawer: LazyComponent<typeof import("../components/CartDrawer.vue")['default']>
export const LazyCategoryGrid: LazyComponent<typeof import("../components/CategoryGrid.vue")['default']>
export const LazyHeroCarousel: LazyComponent<typeof import("../components/HeroCarousel.vue")['default']>
export const LazyHeroDesigner: LazyComponent<typeof import("../components/HeroDesigner.vue")['default']>
export const LazyHeroPreview: LazyComponent<typeof import("../components/HeroPreview.vue")['default']>
export const LazyLimitedTimeOffer: LazyComponent<typeof import("../components/LimitedTimeOffer.vue")['default']>
export const LazyLogoImageStyleEditor: LazyComponent<typeof import("../components/LogoImageStyleEditor.vue")['default']>
export const LazyNavbarDesigner: LazyComponent<typeof import("../components/NavbarDesigner.vue")['default']>
export const LazyNavbarPreview: LazyComponent<typeof import("../components/NavbarPreview.vue")['default']>
export const LazyNewArrivals: LazyComponent<typeof import("../components/NewArrivals.vue")['default']>
export const LazyNewsletterSignup: LazyComponent<typeof import("../components/NewsletterSignup.vue")['default']>
export const LazyPageDesigner: LazyComponent<typeof import("../components/PageDesigner.vue")['default']>
export const LazyTextStyleEditor: LazyComponent<typeof import("../components/TextStyleEditor.vue")['default']>
export const LazyTrendingProducts: LazyComponent<typeof import("../components/TrendingProducts.vue")['default']>
export const LazyTypographyDesigner: LazyComponent<typeof import("../components/TypographyDesigner.vue")['default']>
export const LazyNuxtWelcome: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/welcome.vue")['default']>
export const LazyNuxtLayout: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
export const LazyNuxtErrorBoundary: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']>
export const LazyClientOnly: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/client-only")['default']>
export const LazyDevOnly: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/dev-only")['default']>
export const LazyServerPlaceholder: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyNuxtLink: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-link")['default']>
export const LazyNuxtLoadingIndicator: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
export const LazyNuxtTime: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
export const LazyNuxtImg: LazyComponent<typeof import("../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3.2_ioredis@5.6.1_magicast@0.3.5/node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']>
export const LazyNuxtPicture: LazyComponent<typeof import("../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3.2_ioredis@5.6.1_magicast@0.3.5/node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']>
export const LazyIcon: LazyComponent<typeof import("../node_modules/.pnpm/@nuxt+icon@1.15.0_magicast@0.3.5_vite@6.3.5_@types+node@24.0.13_jiti@2.4.2_terser@5.43.1_yaml_wewk6rl4tdmtvypb2ndrpw4rxe/node_modules/@nuxt/icon/dist/runtime/components/index")['default']>
export const LazyNuxtPage: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/pages/runtime/page")['default']>
export const LazyNoScript: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['NoScript']>
export const LazyLink: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Link']>
export const LazyBase: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Base']>
export const LazyTitle: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Title']>
export const LazyMeta: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Meta']>
export const LazyStyle: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Style']>
export const LazyHead: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Head']>
export const LazyHtml: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Html']>
export const LazyBody: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/head/runtime/components")['Body']>
export const LazyNuxtIsland: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/nuxt-island")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.17.7_@parcel+watcher@2.5.1_@types+node@24.0.13_@vue+compiler-sfc@3.5.17_db0@0.3.2_iore_5f35wah6ch2z7aetrddadxok4y/node_modules/nuxt/dist/app/components/server-placeholder")['default']>

export const componentNames: string[]
