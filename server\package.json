{"name": "server", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon --exec \"npx ts-node\" src/index.ts", "build": "tsc", "start": "node dist/index.js", "start:dev": "ts-node src/index.ts", "seed": "ts-node prisma/seed.ts"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.13.1", "dependencies": {"@prisma/client": "^6.11.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "prisma": "^6.11.1"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/node": "^24.0.13", "nodemon": "^3.1.10", "typescript": "^5.8.3"}}