import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function main(): Promise<void> {
  console.log("🌱 Starting database seeding...");

  // Create SUPERADMIN user
  const hashedPassword = await bcrypt.hash("Onamission#007", 12);

  const superAdmin = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      password: hashedPassword,
      role: "SUPERADMIN",
      firstName: "Timo",
      lastName: "Lambing",
    },
  });

  console.log("✅ SUPERADMIN user created:", superAdmin.email);

  // Create default company settings
  const companySettings = await prisma.companySettings.upsert({
    where: { id: "default" },
    update: {},
    create: {
      id: "default",
      companyName: "Multi Store",
      companyEmail: "<EMAIL>",
      companyPhone: "+****************",
      companyAddress1: "123 Business Street",
      companyCity: "Business City",
      companyState: "BC",
      companyZip: "12345",
      companyCountry: "United States",
      metaTitle: "Multi Store - Your One-Stop Shop",
      metaDescription:
        "Discover amazing products at great prices in our multi-category store.",
      copyrightText: "All rights reserved",
      newsletterTitle: "Subscribe to our newsletter",
      newsletterDescription:
        "Get the latest updates on new products and exclusive offers.",
    },
  });

  console.log("✅ Company settings created");

  // Create sample categories
  const categories: Array<{
    name: string,
    slug: string,
    description: string,
    buttonText: string,
  }> = [
    {
      name: "Electronics",
      slug: "electronics",
      description: "Latest electronic devices and gadgets",
      buttonText: "Shop Electronics",
    },
    {
      name: "Clothing",
      slug: "clothing",
      description: "Fashion and apparel for all occasions",
      buttonText: "Shop Clothing",
    },
    {
      name: "Home & Garden",
      slug: "home-garden",
      description: "Everything for your home and garden",
      buttonText: "Shop Home & Garden",
    },
    {
      name: "Sports & Outdoors",
      slug: "sports-outdoors",
      description: "Gear for sports and outdoor activities",
      buttonText: "Shop Sports",
    },
  ];

  for (const category of categories) {
    await prisma.category.upsert({
      where: { slug: category.slug },
      update: {},
      create: category,
    });
  }

  console.log("✅ Sample categories created");

  // Create sample legal pages
  const legalPages = [
    {
      slug: "terms-of-service",
      title: "Terms of Service",
      content:
        "This is the terms of service content. Please update this content from the admin panel.",
    },
    {
      slug: "privacy-policy",
      title: "Privacy Policy",
      content:
        "This is the privacy policy content. Please update this content from the admin panel.",
    },
    {
      slug: "delivery-information",
      title: "Delivery Information",
      content:
        "This is the delivery information content. Please update this content from the admin panel.",
    },
    {
      slug: "about-us",
      title: "About Us",
      content:
        "This is the about us content. Please update this content from the admin panel.",
    },
    {
      slug: "contact",
      title: "Contact",
      content:
        "This is the contact page content. Please update this content from the admin panel.",
    },
  ];

  for (const page of legalPages) {
    await prisma.legalPage.upsert({
      where: { slug: page.slug },
      update: {},
      create: page,
    });
  }

  console.log("✅ Legal pages created");

  // Create sample benefit cards
  const benefitCards = [
    {
      title: "Free Shipping",
      description: "Free shipping on orders over $50",
      sortOrder: 1,
    },
    {
      title: "24/7 Support",
      description: "Round-the-clock customer support",
      sortOrder: 2,
    },
    {
      title: "Easy Returns",
      description: "30-day hassle-free returns",
      sortOrder: 3,
    },
    {
      title: "Secure Payment",
      description: "Your payment information is safe",
      sortOrder: 4,
    },
  ];

  for (const card of benefitCards) {
    await prisma.benefitCard.create({
      data: card,
    });
  }

  console.log("✅ Benefit cards created");

  console.log("🎉 Database seeding completed!");
}

main()
  .catch((e) => {
    console.error("❌ Error during seeding:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
