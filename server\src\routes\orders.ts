import express from "express";
import { createOrder, getUserOrders } from "../controllers/orders.controller";
import { authenticate, optionalAuth } from "../middleware/auth";
import { wrapController } from "../utils/wrapController";

const router: express.Router = express.Router();

// Orders routes
router.post("/", optionalAuth, wrapController(createOrder));
router.get("/my-orders", authenticate, wrapController(getUserOrders));

export default router;
