import { defineStore } from "pinia";

interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  isActive: boolean;
  _count?: {
    products: number;
  };
}

interface CategoriesState {
  categories: Category[];
  currentCategory: Category | null;
  loading: boolean;
}

interface CategoriesResponse {
  categories: Category[];
}

interface CategoryResponse {
  category: Category;
}

export const useCategoriesStore = defineStore("categories", {
  state: (): CategoriesState => ({
    categories: [],
    currentCategory: null,
    loading: false,
  }),

  getters: {
    activeCategories: (state): Category[] =>
      state.categories.filter((category) => category.isActive),

    categoryById:
      (state) =>
      (id: string): Category | undefined =>
        state.categories.find((category) => category.id === id),

    categoryBySlug:
      (state) =>
      (slug: string): Category | undefined =>
        state.categories.find((category) => category.slug === slug),

    categoriesWithProducts: (state): Category[] =>
      state.categories.filter(
        (category) => category._count?.products && category._count.products > 0
      ),
  },

  actions: {
    async fetchCategories(): Promise<CategoriesResponse> {
      this.loading = true;
      try {
        const api = useApi();
        const response = await api.get<CategoriesResponse>(
          "/public/categories",
          {
            showLoading: false, // We handle loading in the store
            showError: true,
          }
        );
        this.categories = response.categories;
        return response;
      } catch (error: unknown) {
        console.error("Fetch categories error:", error);

        // Graceful degradation for SSR
        if (import.meta.server) {
          console.warn("SSR: Using empty categories due to API error");
          const defaultResponse: CategoriesResponse = {
            categories: [],
          };
          this.categories = [];
          return defaultResponse;
        }

        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchCategoryBySlug(slug: string): Promise<Category> {
      try {
        const api = useApi();
        const response = await api.get<CategoryResponse>(
          `/public/categories/${slug}/products?limit=1`
        );
        this.currentCategory = response.category;
        return response.category;
      } catch (error: unknown) {
        console.error("Fetch category error:", error);
        throw error;
      }
    },

    clearCurrentCategory(): void {
      this.currentCategory = null;
    },
  },
});
