import { Request, Response, NextFunction } from "express";
import { PrismaClient } from "@prisma/client";
import { createError } from "../utils/wrapController";
import { getFileUrl } from "../middleware/upload";

const prisma = new PrismaClient();

// Request interfaces
export interface NewsletterRequest {
  email: string;
}

export interface CategoryProductsQuery {
  page?: string;
  limit?: string;
}

/**
 * GET /api/public/carousel
 * Get carousel slides
 */
export const getCarouselSlides = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const slides = await prisma.carouselSlide.findMany({
      where: { isActive: true },
      orderBy: { sortOrder: "asc" },
    });

    res.json({ slides });
  } catch (error) {
    console.error("Get carousel error:", error);
    next(createError(500, "Failed to get carousel slides"));
  }
};

/**
 * GET /api/public/benefits
 * Get benefit cards
 */
export const getBenefitCards = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const benefits = await prisma.benefitCard.findMany({
      where: { isActive: true },
      orderBy: { sortOrder: "asc" },
    });

    res.json({ benefits });
  } catch (error) {
    console.error("Get benefits error:", error);
    next(createError(500, "Failed to get benefit cards"));
  }
};

/**
 * GET /api/public/categories
 * Get all active categories
 */
export const getPublicCategories = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const categories = await prisma.category.findMany({
      where: { isActive: true },
      orderBy: { sortOrder: "asc" },
      include: {
        _count: {
          select: { products: true },
        },
      },
    });

    res.json({ categories });
  } catch (error) {
    console.error("Get categories error:", error);
    next(createError(500, "Failed to get categories"));
  }
};

/**
 * GET /api/public/products/trending
 * Get trending products
 */
export const getTrendingProducts = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const products = await prisma.product.findMany({
      where: {
        isActive: true,
        isTrending: true,
      },
      include: {
        images: {
          orderBy: { sortOrder: "asc" },
          take: 1,
        },
        category: {
          select: { name: true, slug: true },
        },
      },
      orderBy: { createdAt: "desc" },
      take: 8,
    });

    res.json({ products });
  } catch (error) {
    console.error("Get trending products error:", error);
    next(createError(500, "Failed to get trending products"));
  }
};

/**
 * GET /api/public/products/new-arrivals
 * Get new arrival products
 */
export const getNewArrivals = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const products = await prisma.product.findMany({
      where: {
        isActive: true,
        isNewArrival: true,
      },
      include: {
        images: {
          orderBy: { sortOrder: "asc" },
          take: 1,
        },
        category: {
          select: { name: true, slug: true },
        },
      },
      orderBy: { createdAt: "desc" },
      take: 8,
    });

    res.json({ products });
  } catch (error) {
    console.error("Get new arrivals error:", error);
    next(createError(500, "Failed to get new arrival products"));
  }
};

/**
 * GET /api/public/products/:slug
 * Get product by slug
 */
export const getProductBySlug = async (
  req: Request<{ slug: string }>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { slug } = req.params;

    const product = await prisma.product.findUnique({
      where: { slug, isActive: true },
      include: {
        images: {
          orderBy: { sortOrder: "asc" },
        },
        variants: true,
        accordions: {
          orderBy: { sortOrder: "asc" },
        },
        category: {
          select: { name: true, slug: true },
        },
      },
    });

    if (!product) {
      next(createError(404, "Product not found"));
      return;
    }

    // Get related products from same category
    const relatedProducts = await prisma.product.findMany({
      where: {
        categoryId: product.categoryId,
        isActive: true,
        id: { not: product.id },
      },
      include: {
        images: {
          orderBy: { sortOrder: "asc" },
          take: 1,
        },
      },
      take: 4,
    });

    res.json({
      product,
      relatedProducts,
    });
  } catch (error) {
    console.error("Get product error:", error);
    next(createError(500, "Failed to get product"));
  }
};

/**
 * GET /api/public/categories/:slug/products
 * Get products by category with pagination
 */
export const getCategoryProducts = async (
  req: Request<{ slug: string }, {}, {}, CategoryProductsQuery>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { slug } = req.params;
    const page = parseInt(req.query.page || "1");
    const limit = parseInt(req.query.limit || "9");
    const skip = (page - 1) * limit;

    // Get category
    const category = await prisma.category.findUnique({
      where: { slug, isActive: true },
    });

    if (!category) {
      next(createError(404, "Category not found"));
      return;
    }

    // Get products with pagination
    const [products, totalCount] = await Promise.all([
      prisma.product.findMany({
        where: {
          categoryId: category.id,
          isActive: true,
        },
        include: {
          images: {
            orderBy: { sortOrder: "asc" },
            take: 1,
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.product.count({
        where: {
          categoryId: category.id,
          isActive: true,
        },
      }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    res.json({
      category,
      products,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    console.error("Get category products error:", error);
    next(createError(500, "Failed to get category products"));
  }
};

/**
 * GET /api/public/limited-time-offer
 * Get active limited time offer
 */
export const getLimitedTimeOffer = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const offer = await prisma.limitedTimeOffer.findFirst({
      where: {
        isActive: true,
        OR: [{ endDate: null }, { endDate: { gte: new Date() } }],
      },
      orderBy: { createdAt: "desc" },
    });

    res.json({ offer });
  } catch (error) {
    console.error("Get limited time offer error:", error);
    next(createError(500, "Failed to get limited time offer"));
  }
};

/**
 * POST /api/public/newsletter
 * Subscribe to newsletter
 */
export const subscribeToNewsletter = async (
  req: Request<{}, {}, NewsletterRequest>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const { email } = req.body;

  try {
    if (!email) {
      next(createError(400, "Email is required"));
      return;
    }

    // Check if email already exists
    const existingSubscriber = await prisma.newsletter.findUnique({
      where: { email: email.toLowerCase() },
    });

    if (existingSubscriber) {
      next(createError(409, "Email already subscribed"));
      return;
    }

    // Create newsletter subscription
    await prisma.newsletter.create({
      data: {
        email: email.toLowerCase(),
      },
    });

    res.status(201).json({
      message: "Successfully subscribed to newsletter",
    });
  } catch (error) {
    console.error("Newsletter subscription error:", error);
    next(createError(500, "Failed to subscribe to newsletter"));
  }
};

/**
 * GET /api/public/legal/:slug
 * Get legal page content
 */
export const getLegalPage = async (
  req: Request<{ slug: string }>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { slug } = req.params;

    const page = await prisma.legalPage.findUnique({
      where: { slug, isActive: true },
    });

    if (!page) {
      next(createError(404, "Page not found"));
      return;
    }

    res.json({ page });
  } catch (error) {
    console.error("Get legal page error:", error);
    next(createError(500, "Failed to get legal page"));
  }
};

/**
 * GET /api/public/company-settings
 * Get company settings
 */
export const getCompanySettings = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const settings = await prisma.companySettings.findFirst();

    // Convert relative logoUrl to full URL
    if (settings && settings.logoUrl) {
      settings.logoUrl = getFileUrl(settings.logoUrl);
    }

    res.json({ settings });
  } catch (error) {
    console.error("Get company settings error:", error);
    next(createError(500, "Failed to get company settings"));
  }
};

/**
 * GET /api/public/homepage-design
 * Get homepage design settings for public pages
 */
export const getHomepageDesign = async (
  _req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const settings = await prisma.companySettings.findFirst();

    if (!settings) {
      res.json({ design: null });
      return;
    }

    // Extract only the design-related fields needed for public pages
    const design = {
      navbarDesign: settings.navbarDesign,
      heroDesign: settings.heroDesign,
      benefitCardsDesign: (settings as any).benefitCardsDesign,
      cardsDesign: settings.cardsDesign,
      typographyDesign: settings.typographyDesign,
      pageDesign: settings.pageDesign,
      buttonDesign: settings.buttonDesign,
    };

    // Process hero design slides to convert relative image URLs to full URLs
    if (design.heroDesign && typeof design.heroDesign === "object") {
      const heroDesign = design.heroDesign as any;
      if (heroDesign.slides && Array.isArray(heroDesign.slides)) {
        heroDesign.slides = heroDesign.slides.map((slide: any) => {
          // Clean up invalid URLs but keep the slide content
          let cleanImage = slide.image;

          if (!slide.image) {
            cleanImage = null;
          } else if (slide.image.startsWith("blob:")) {
            cleanImage = null; // Remove blob URL but keep slide
          } else if (
            slide.image.startsWith("/") &&
            !slide.image.startsWith("/uploads/")
          ) {
            // Remove invalid paths that start with / but aren't proper upload paths
            cleanImage = null;
          } else if (
            slide.image.startsWith("carousel/") ||
            slide.image.startsWith("logos/")
          ) {
            // Convert relative paths to full URLs
            cleanImage = getFileUrl(slide.image);
          } else if (slide.image.startsWith("http")) {
            // Already a full URL, keep as is
            cleanImage = slide.image;
          } else {
            // Unknown format, remove it
            cleanImage = null;
          }

          return {
            ...slide,
            image: cleanImage,
          };
        });
      }
    }

    // Process benefit cards design to convert relative image URLs to full URLs
    if (
      design.benefitCardsDesign &&
      typeof design.benefitCardsDesign === "object"
    ) {
      const benefitCardsDesign = design.benefitCardsDesign as any;
      if (benefitCardsDesign.cards && Array.isArray(benefitCardsDesign.cards)) {
        benefitCardsDesign.cards = benefitCardsDesign.cards.map((card: any) => {
          // Clean up invalid URLs but keep the card content
          let cleanImage = card.image;

          if (!card.image) {
            cleanImage = null;
          } else if (card.image.startsWith("blob:")) {
            cleanImage = null; // Remove blob URL but keep card
          } else if (
            card.image.startsWith("/") &&
            !card.image.startsWith("/uploads/")
          ) {
            // Remove invalid paths that start with / but aren't proper upload paths
            cleanImage = null;
          } else if (
            card.image.startsWith("benefits/") ||
            card.image.startsWith("carousel/") ||
            card.image.startsWith("logos/")
          ) {
            // Convert relative paths to full URLs
            cleanImage = getFileUrl(card.image);
          } else if (card.image.startsWith("http")) {
            // Already a full URL, keep as is
            cleanImage = card.image;
          } else {
            // Unknown format, remove it
            cleanImage = null;
          }

          return {
            ...card,
            image: cleanImage,
          };
        });
      }
    }

    res.json({ design });
  } catch (error) {
    console.error("Get homepage design error:", error);
    next(createError(500, "Failed to get homepage design"));
  }
};
