<template>
  <div class="drawer drawer-end z-50">
    <input
      id="cart-drawer"
      type="checkbox"
      class="drawer-toggle"
      :checked="cartStore.isOpen"
      @change="cartStore.isOpen = $event.target.checked"
    />

    <div class="drawer-side">
      <label
        for="cart-drawer"
        aria-label="close sidebar"
        class="drawer-overlay"
      ></label>

      <div class="bg-base-200 text-base-content min-h-full w-80 p-4">
        <!-- Header -->
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-bold">Shopping Cart</h2>
          <button
            @click="cartStore.closeCart()"
            class="btn btn-ghost btn-sm btn-circle"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        <!-- Cart Items -->
        <div v-if="!cartStore.isEmpty" class="flex-1 overflow-y-auto">
          <div class="space-y-4">
            <div
              v-for="item in cartStore.items"
              :key="item.id"
              class="card bg-base-100 shadow-sm"
            >
              <div class="card-body p-4">
                <div class="flex gap-3">
                  <!-- Product Image -->
                  <div class="avatar">
                    <div class="w-16 h-16 rounded">
                      <NuxtImg
                        v-if="item.product.images?.[0]?.url"
                        :src="item.product.images[0].url"
                        :alt="item.product.name"
                        class="object-cover"
                      />
                      <div
                        v-else
                        class="bg-base-300 flex items-center justify-center"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-6 w-6 text-base-content/50"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>

                  <!-- Product Details -->
                  <div class="flex-1">
                    <h3 class="font-medium text-sm">{{ item.product.name }}</h3>
                    <p class="text-primary font-semibold">
                      ${{ item.product.price }}
                    </p>

                    <!-- Variants -->
                    <div
                      v-if="Object.keys(item.selectedVariants).length > 0"
                      class="text-xs text-base-content/70 mt-1"
                    >
                      <span
                        v-for="(value, key) in item.selectedVariants"
                        :key="key"
                        class="mr-2"
                      >
                        {{ key }}: {{ value }}
                      </span>
                    </div>

                    <!-- Quantity Controls -->
                    <div class="flex items-center gap-2 mt-2">
                      <button
                        @click="
                          cartStore.updateQuantity(item.id, item.quantity - 1)
                        "
                        class="btn btn-xs btn-circle btn-outline"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3 w-3"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M20 12H4"
                          />
                        </svg>
                      </button>
                      <span class="text-sm font-medium w-8 text-center">{{
                        item.quantity
                      }}</span>
                      <button
                        @click="
                          cartStore.updateQuantity(item.id, item.quantity + 1)
                        "
                        class="btn btn-xs btn-circle btn-outline"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3 w-3"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                          />
                        </svg>
                      </button>
                      <button
                        @click="cartStore.removeItem(item.id)"
                        class="btn btn-xs btn-ghost text-error ml-auto"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3 w-3"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty Cart -->
        <div
          v-else
          class="flex-1 flex flex-col items-center justify-center text-center py-8"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-16 w-16 text-base-content/30 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
            />
          </svg>
          <h3 class="text-lg font-medium mb-2">Your cart is empty</h3>
          <p class="text-base-content/70 mb-4">
            Add some products to get started
          </p>
          <button @click="cartStore.closeCart()" class="btn btn-primary btn-sm">
            Continue Shopping
          </button>
        </div>

        <!-- Cart Footer -->
        <div v-if="!cartStore.isEmpty" class="border-t pt-4 mt-4">
          <!-- Total -->
          <div class="flex justify-between items-center mb-4">
            <span class="font-medium">Total:</span>
            <span class="text-lg font-bold text-primary">{{
              cartStore.formattedTotal
            }}</span>
          </div>

          <!-- Actions -->
          <div class="space-y-2">
            <NuxtLink
              to="/checkout"
              class="btn btn-primary btn-block"
              @click="cartStore.closeCart()"
            >
              Checkout
            </NuxtLink>
            <button
              @click="cartStore.clearCart()"
              class="btn btn-outline btn-error btn-block btn-sm"
            >
              Clear Cart
            </button>
          </div>

          <!-- Powered by Stripe -->
          <div class="text-center mt-4">
            <p class="text-xs text-base-content/50">Powered by Stripe</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const cartStore = useCartStore();

// Watch for cart open state changes
watch(
  () => cartStore.isOpen,
  (isOpen) => {
    const checkbox = document.getElementById("cart-drawer");
    if (checkbox) {
      checkbox.checked = isOpen;
    }
  }
);
</script>
