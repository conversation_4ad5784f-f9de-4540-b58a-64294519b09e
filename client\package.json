{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --open", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/icon": "^1.15.0", "@nuxt/image": "^1.10.0", "@nuxtjs/tailwindcss": "^6.14.0", "@pinia/nuxt": "^0.11.1", "@vueuse/core": "^13.5.0", "daisyui": "^5.0.46", "multer": "^2.0.1", "nuxt": "^3.17.6", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "vue": "^3.5.17", "vue-router": "^4.5.1", "vue3-colorpicker": "^2.3.0"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0", "devDependencies": {"@iconify-json/heroicons": "^1.2.2"}}