<!-- client/pages/index.vue -->
<template>
  <div>
    <!-- Hero Carousel Section -->
    <section id="hero" :style="getSectionStyle('hero')">
      <HeroCarousel />
    </section>

    <!-- Benefit Cards Section -->
    <section class="py-16" :style="getSectionStyle('benefits')">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <BenefitCards />
      </div>
    </section>

    <!-- Trending Products Section -->
    <section id="trending" class="py-16" :style="getSectionStyle('trending')">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">Trending Now</h2>
          <p class="text-base-content/70">Discover what's popular right now</p>
        </div>
        <TrendingProducts />
      </div>
    </section>

    <!-- Categories Section -->
    <section
      id="categories"
      class="py-16"
      :style="getSectionStyle('categories')"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">Shop by Categories</h2>
          <p class="text-base-content/70">
            Browse our wide range of product categories
          </p>
        </div>
        <CategoryGrid />
      </div>
    </section>

    <!-- Limited Time Offer Section -->
    <section
      class="py-16 text-primary-content"
      :style="getSectionStyle('limitedOffer')"
    >
      <LimitedTimeOffer />
    </section>

    <!-- New Arrivals Section -->
    <section
      id="new-arrivals"
      class="py-16"
      :style="getSectionStyle('newArrivals')"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">New Arrivals</h2>
          <p class="text-base-content/70">Check out our latest products</p>
        </div>
        <NewArrivals />
      </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-16" :style="getSectionStyle('newsletter')">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <NewsletterSignup />
      </div>
    </section>
  </div>
</template>

<script setup>
// Meta tags for SEO
useHead({
  title: "Multi Store - Your One-Stop Shop",
  meta: [
    {
      name: "description",
      content:
        "Discover amazing products at great prices in our multi-category store.",
    },
  ],
});

// Initialize stores and fetch data
const contentStore = useContentStore();
const productsStore = useProductsStore();
const categoriesStore = useCategoriesStore();

// Get homepage design from store
const homepageDesign = computed(() => contentStore.homepageDesign);
const sectionBackgrounds = computed(
  () => homepageDesign.value?.sectionBackgrounds
);

// Default section backgrounds
const defaultBackgrounds = {
  hero: "transparent",
  benefits: "#f8fafc",
  trending: "#f1f5f9",
  categories: "#f8fafc",
  limitedOffer: "linear-gradient(to right, #3b82f6, #8b5cf6)",
  newArrivals: "#f1f5f9",
  newsletter: "#f8fafc",
};

// Get section style
const getSectionStyle = (sectionName) => {
  const backgrounds = sectionBackgrounds.value || defaultBackgrounds;
  const background =
    backgrounds[sectionName] || defaultBackgrounds[sectionName];

  if (background.startsWith("linear-gradient")) {
    return { background };
  } else if (background === "transparent") {
    return { backgroundColor: "transparent" };
  } else {
    return { backgroundColor: background };
  }
};

// Fetch homepage data
onMounted(async () => {
  try {
    await Promise.all([
      contentStore.initializeHomepageContent(),
      contentStore.fetchHomepageDesign(),
      productsStore.fetchTrendingProducts(),
      productsStore.fetchNewArrivals(),
      categoriesStore.fetchCategories(),
    ]);
  } catch (error) {
    console.error("Error loading homepage data:", error);
  }
});
</script>
