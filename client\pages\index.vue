<template>
  <div>
    <!-- Hero Carousel Section -->
    <section id="hero">
      <HeroCarousel />
    </section>

    <!-- Benefit Cards Section -->
    <section class="py-16 bg-base-100">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <BenefitCards />
      </div>
    </section>

    <!-- Trending Products Section -->
    <section id="trending" class="py-16 bg-base-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">Trending Now</h2>
          <p class="text-base-content/70">Discover what's popular right now</p>
        </div>
        <TrendingProducts />
      </div>
    </section>

    <!-- Categories Section -->
    <section id="categories" class="py-16 bg-base-100">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">Shop by Categories</h2>
          <p class="text-base-content/70">
            Browse our wide range of product categories
          </p>
        </div>
        <CategoryGrid />
      </div>
    </section>

    <!-- Limited Time Offer Section -->
    <section
      class="py-16 bg-gradient-to-r from-primary to-secondary text-primary-content"
    >
      <LimitedTimeOffer />
    </section>

    <!-- New Arrivals Section -->
    <section id="new-arrivals" class="py-16 bg-base-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">New Arrivals</h2>
          <p class="text-base-content/70">Check out our latest products</p>
        </div>
        <NewArrivals />
      </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-16 bg-base-100">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <NewsletterSignup />
      </div>
    </section>
  </div>
</template>

<script setup>
// Meta tags for SEO
useHead({
  title: "Multi Store - Your One-Stop Shop",
  meta: [
    {
      name: "description",
      content:
        "Discover amazing products at great prices in our multi-category store.",
    },
  ],
});

// Initialize stores and fetch data
const contentStore = useContentStore();
const productsStore = useProductsStore();
const categoriesStore = useCategoriesStore();

// Fetch homepage data
onMounted(async () => {
  try {
    await Promise.all([
      contentStore.initializeHomepageContent(),
      contentStore.fetchHomepageDesign(),
      productsStore.fetchTrendingProducts(),
      productsStore.fetchNewArrivals(),
      categoriesStore.fetchCategories(),
    ]);
  } catch (error) {
    console.error("Error loading homepage data:", error);
  }
});
</script>
