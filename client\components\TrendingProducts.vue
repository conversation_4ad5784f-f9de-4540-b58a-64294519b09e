<template>
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
    <div 
      v-for="product in displayProducts" 
      :key="product.id"
      class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow duration-300"
    >
      <figure class="relative">
        <!-- Product Image -->
        <NuxtImg 
          :src="product.images?.[0]?.url || 'https://img.daisyui.com/images/stock/photo-1606107557195-0e29a4b5b4aa.webp'" 
          :alt="product.name"
          class="w-full h-48 object-cover"
        />
        
        <!-- Badge -->
        <div v-if="product.badgeText" class="badge badge-secondary absolute top-2 left-2">
          {{ product.badgeText }}
        </div>
      </figure>
      
      <div class="card-body p-4">
        <!-- Product Title -->
        <h3 class="card-title text-base">{{ product.name }}</h3>
        
        <!-- Price and CTA -->
        <div class="card-actions justify-between items-center mt-4">
          <div class="flex flex-col">
            <span class="text-lg font-bold text-primary">${{ product.price }}</span>
            <span v-if="product.comparePrice" class="text-sm text-base-content/50 line-through">
              ${{ product.comparePrice }}
            </span>
          </div>
          
          <div class="flex gap-2">
            <NuxtLink 
              :to="`/products/${product.slug}`" 
              class="btn btn-primary btn-sm"
            >
              View
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </NuxtLink>
            
            <button 
              @click="addToCart(product)"
              class="btn btn-outline btn-sm"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const productsStore = useProductsStore()
const cartStore = useCartStore()

// Get trending products from store
const storeTrendingProducts = computed(() => productsStore.trendingProducts)

// Default products if none are loaded
const defaultProducts = [
  {
    id: 'default-1',
    name: 'Wireless Headphones',
    slug: 'wireless-headphones',
    price: '99.99',
    comparePrice: '129.99',
    badgeText: 'Sale',
    images: [{ url: 'https://img.daisyui.com/images/stock/photo-1606107557195-0e29a4b5b4aa.webp' }]
  },
  {
    id: 'default-2',
    name: 'Smart Watch',
    slug: 'smart-watch',
    price: '199.99',
    badgeText: 'New',
    images: [{ url: 'https://img.daisyui.com/images/stock/photo-1572635148818-ef6fd45eb394.webp' }]
  },
  {
    id: 'default-3',
    name: 'Laptop Stand',
    slug: 'laptop-stand',
    price: '49.99',
    images: [{ url: 'https://img.daisyui.com/images/stock/photo-1494253109108-2e30c049369b.webp' }]
  },
  {
    id: 'default-4',
    name: 'Coffee Mug',
    slug: 'coffee-mug',
    price: '19.99',
    badgeText: 'Popular',
    images: [{ url: 'https://img.daisyui.com/images/stock/photo-1550258987-190a2d41a8ba.webp' }]
  }
]

// Use default products if no products are available
const displayProducts = computed(() => {
  return storeTrendingProducts.value.length > 0 ? storeTrendingProducts.value.slice(0, 4) : defaultProducts
})

// Add to cart function
const addToCart = (product) => {
  cartStore.addItem(product, 1)
}
</script>
