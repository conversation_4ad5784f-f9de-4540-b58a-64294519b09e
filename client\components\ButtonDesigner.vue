<template>
  <div class="space-y-8">
    <!-- Section Header -->
    <div class="border-b pb-4">
      <h2 class="text-2xl font-semibold">Button Design</h2>
      <p class="text-base-content/70 mt-1">
        Customize button styles throughout your website
      </p>
    </div>

    <!-- Button Types -->
    <div class="space-y-6">
      <div
        v-for="(buttonType, key) in localDesign"
        :key="key"
        class="card bg-base-100 shadow-sm border"
      >
        <div class="card-body">
          <h3 class="card-title text-lg">
            {{ key.charAt(0).toUpperCase() + key.slice(1) }} Button
          </h3>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Style -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Style</span>
              </label>
              <select
                v-model="localDesign[key].style"
                class="select select-bordered"
                @change="updateDesign"
              >
                <option value="rounded">Rounded</option>
                <option value="square">Square</option>
                <option value="circle">Circle</option>
                <option value="outline">Outline</option>
                <option value="ghost">Ghost</option>
                <option value="link">Link Style</option>
              </select>
            </div>

            <!-- Size -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Size</span>
              </label>
              <select
                v-model="localDesign[key].size"
                class="select select-bordered"
                @change="updateDesign"
              >
                <option value="xs">Extra Small</option>
                <option value="sm">Small</option>
                <option value="default">Default</option>
                <option value="lg">Large</option>
                <option value="xl">Extra Large</option>
              </select>
            </div>

            <!-- Colors -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Colors</span>
              </label>
              <div class="grid grid-cols-3 gap-2">
                <div class="form-control">
                  <label class="label py-1">
                    <span class="label-text-alt">Background</span>
                  </label>
                  <input
                    v-model="localDesign[key].colors.bg"
                    type="color"
                    class="input input-bordered w-full h-8"
                    @input="updateDesign"
                  />
                </div>
                <div class="form-control">
                  <label class="label py-1">
                    <span class="label-text-alt">Text</span>
                  </label>
                  <input
                    v-model="localDesign[key].colors.text"
                    type="color"
                    class="input input-bordered w-full h-8"
                    @input="updateDesign"
                  />
                </div>
                <div class="form-control">
                  <label class="label py-1">
                    <span class="label-text-alt">Border</span>
                  </label>
                  <input
                    v-model="localDesign[key].colors.border"
                    type="color"
                    class="input input-bordered w-full h-8"
                    @input="updateDesign"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Preview -->
          <div class="mt-6 p-4 bg-base-200 rounded-lg">
            <div class="flex gap-4 items-center">
              <button :class="getButtonClass(key)" :style="getButtonStyle(key)">
                Sample {{ key.charAt(0).toUpperCase() + key.slice(1) }} Button
              </button>
              <button
                :class="[getButtonClass(key), 'btn-disabled']"
                :style="getButtonStyle(key)"
                disabled
              >
                Disabled State
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Global Preview -->
    <div class="card bg-base-100 shadow-sm border">
      <div class="card-body">
        <h3 class="card-title text-lg">All Buttons Preview</h3>
        <div class="flex flex-wrap gap-4">
          <button
            v-for="(buttonType, key) in localDesign"
            :key="key"
            :class="getButtonClass(key)"
            :style="getButtonStyle(key)"
          >
            {{ key.charAt(0).toUpperCase() + key.slice(1) }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  design: {
    type: Object,
    required: true,
  },
});

// Emits
const emit = defineEmits(["update:design", "update"]);

// Local state
const localDesign = ref({
  primary: {
    style: "rounded",
    size: "default",
    colors: { bg: "#3b82f6", text: "#ffffff", border: "#3b82f6" },
  },
  secondary: {
    style: "rounded",
    size: "default",
    colors: { bg: "#6b7280", text: "#ffffff", border: "#6b7280" },
  },
  ...(props.design || {}),
});

// Methods
const updateDesign = () => {
  emit("update:design", { ...localDesign.value });
  emit("update");
};

const getButtonClass = (buttonType) => {
  const config = localDesign.value[buttonType];
  let classes = ["btn"];

  // Add style classes
  switch (config.style) {
    case "square":
      classes.push("btn-square");
      break;
    case "circle":
      classes.push("btn-circle");
      break;
    case "outline":
      classes.push("btn-outline");
      break;
    case "ghost":
      classes.push("btn-ghost");
      break;
    case "link":
      classes.push("btn-link");
      break;
    default:
      // rounded is default
      break;
  }

  // Add size classes
  switch (config.size) {
    case "xs":
      classes.push("btn-xs");
      break;
    case "sm":
      classes.push("btn-sm");
      break;
    case "lg":
      classes.push("btn-lg");
      break;
    case "xl":
      classes.push("btn-xl");
      break;
    default:
      // default size
      break;
  }

  return classes.join(" ");
};

const getButtonStyle = (buttonType) => {
  const config = localDesign.value[buttonType];
  return {
    backgroundColor: config.colors.bg,
    color: config.colors.text,
    borderColor: config.colors.border,
  };
};

// Watch for prop changes
watch(
  () => props.design,
  (newDesign) => {
    localDesign.value = { ...newDesign };
  },
  { deep: true }
);
</script>
