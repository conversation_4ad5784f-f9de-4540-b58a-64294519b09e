import express from "express";
import { PrismaClient } from "@prisma/client";
import {
  getDashboard,
  getAdminProducts,
  createProduct,
  getAdminOrders,
  updateOrderStatus,
} from "../controllers/admin.controller";
import { authenticate, requireAdmin } from "../middleware/auth";
import { wrapController } from "../utils/wrapController";

const prisma = new PrismaClient();
const router: express.Router = express.Router();

// Apply authentication and admin requirement to all admin routes
router.use(authenticate);
router.use(requireAdmin);

// Dashboard routes
router.get("/dashboard", wrapController(getDashboard));

// Placeholder routes for admin functionality
// These will be expanded in later tasks

// ===== PRODUCTS MANAGEMENT =====

// Products management routes
router.get("/products", wrapController(getAdminProducts));
router.post("/products", wrapController(createProduct));

// Orders management routes
router.get("/orders", wrapController(getAdminOrders));
router.put("/orders/:id/status", wrapController(updateOrderStatus));

// Legacy routes (to be refactored later)
router.get("/products-legacy", async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;
    const search =
      typeof req.query.search === "string" ? req.query.search : undefined;

    const where: any = {
      ...(search && {
        OR: [
          { name: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
        ],
      }),
    };

    const [products, totalCount] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          category: { select: { name: true, slug: true } },
          images: { orderBy: { sortOrder: "asc" } },
          variants: true,
          _count: { select: { orderItems: true } },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.product.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    res.json({
      products,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    console.error("Get admin products error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to get products",
    });
  }
});

/**
 * POST /api/admin/products
 * Create new product
 */
router.post("/products", async (req, res) => {
  try {
    const {
      name,
      slug,
      description,
      shortDescription,
      price,
      comparePrice,
      sku,
      barcode,
      trackQuantity,
      quantity,
      weight,
      isActive,
      isFeatured,
      isTrending,
      isNewArrival,
      badgeText,
      metaTitle,
      metaDescription,
      categoryId,
      images,
      variants,
      accordions,
    } = req.body;

    // Check if slug already exists
    const existingProduct = await prisma.product.findUnique({
      where: { slug },
    });

    if (existingProduct) {
      return res.status(409).json({
        error: "Conflict",
        message: "Product with this slug already exists",
      });
    }

    const product = await prisma.product.create({
      data: {
        name,
        slug,
        description,
        shortDescription,
        price,
        comparePrice,
        sku,
        barcode,
        trackQuantity,
        quantity,
        weight,
        isActive,
        isFeatured,
        isTrending,
        isNewArrival,
        badgeText,
        metaTitle,
        metaDescription,
        categoryId,
        images: images
          ? {
              create: images.map(
                (img: { url: string; altText?: string }, index: number) => ({
                  url: img.url,
                  altText: img.altText,
                  sortOrder: index,
                })
              ),
            }
          : undefined,
        variants: variants
          ? {
              create: variants.map(
                (variant: {
                  name: string;
                  value: string;
                  price?: number;
                  sku?: string;
                  quantity?: number;
                }) => ({
                  name: variant.name,
                  value: variant.value,
                })
              ),
            }
          : undefined,
        accordions: accordions
          ? {
              create: accordions.map(
                (acc: { title: string; content: string }, index: number) => ({
                  title: acc.title,
                  content: acc.content,
                  sortOrder: index,
                })
              ),
            }
          : undefined,
      },
      include: {
        category: { select: { name: true, slug: true } },
        images: { orderBy: { sortOrder: "asc" } },
        variants: true,
        accordions: { orderBy: { sortOrder: "asc" } },
      },
    });

    res.status(201).json({
      message: "Product created successfully",
      product,
    });
  } catch (error) {
    console.error("Create product error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to create product",
    });
  }
});

/**
 * PUT /api/admin/products/:id
 * Update product
 */
router.put("/products/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Remove nested objects for direct update
    const { images, variants, accordions, ...productData } = updateData;

    const product = await prisma.product.update({
      where: { id },
      data: productData,
      include: {
        category: { select: { name: true, slug: true } },
        images: { orderBy: { sortOrder: "asc" } },
        variants: true,
        accordions: { orderBy: { sortOrder: "asc" } },
      },
    });

    res.json({
      message: "Product updated successfully",
      product,
    });
  } catch (error) {
    console.error("Update product error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to update product",
    });
  }
});

/**
 * DELETE /api/admin/products/:id
 * Delete product
 */
router.delete("/products/:id", async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.product.delete({
      where: { id },
    });

    res.json({
      message: "Product deleted successfully",
    });
  } catch (error) {
    console.error("Delete product error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to delete product",
    });
  }
});

// ===== CATEGORIES MANAGEMENT =====

/**
 * GET /api/admin/categories
 * Get all categories
 */
router.get("/categories", async (req, res) => {
  try {
    const categories = await prisma.category.findMany({
      include: {
        _count: { select: { products: true } },
      },
      orderBy: { sortOrder: "asc" },
    });

    res.json({ categories });
  } catch (error) {
    console.error("Get admin categories error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to get categories",
    });
  }
});

/**
 * POST /api/admin/categories
 * Create new category
 */
router.post("/categories", async (req, res) => {
  try {
    const { name, slug, description, image, buttonText, isActive, sortOrder } =
      req.body;

    // Check if slug already exists
    const existingCategory = await prisma.category.findUnique({
      where: { slug },
    });

    if (existingCategory) {
      return res.status(409).json({
        error: "Conflict",
        message: "Category with this slug already exists",
      });
    }

    const category = await prisma.category.create({
      data: {
        name,
        slug,
        description,
        image,
        buttonText,
        isActive,
        sortOrder,
      },
    });

    res.status(201).json({
      message: "Category created successfully",
      category,
    });
  } catch (error) {
    console.error("Create category error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to create category",
    });
  }
});

/**
 * PUT /api/admin/categories/:id
 * Update category
 */
router.put("/categories/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const category = await prisma.category.update({
      where: { id },
      data: updateData,
    });

    res.json({
      message: "Category updated successfully",
      category,
    });
  } catch (error) {
    console.error("Update category error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to update category",
    });
  }
});

/**
 * DELETE /api/admin/categories/:id
 * Delete category
 */
router.delete("/categories/:id", async (req, res) => {
  try {
    const { id } = req.params;

    // Check if category has products
    const productCount = await prisma.product.count({
      where: { categoryId: id },
    });

    if (productCount > 0) {
      return res.status(400).json({
        error: "Bad Request",
        message: "Cannot delete category with existing products",
      });
    }

    await prisma.category.delete({
      where: { id },
    });

    res.json({
      message: "Category deleted successfully",
    });
  } catch (error) {
    console.error("Delete category error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to delete category",
    });
  }
});

export default router;
