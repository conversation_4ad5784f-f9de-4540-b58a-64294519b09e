// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 20.7.2025, 17:14:49
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.ts";
const config = [
{"content":{"files":["C:/Users/<USER>/OneDrive/Töölaud/DevSkills OÜ/veebipood/multi-store/client/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Töölaud/DevSkills OÜ/veebipood/multi-store/client/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Töölaud/DevSkills OÜ/veebipood/multi-store/client/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Töölaud/DevSkills OÜ/veebipood/multi-store/client/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Töölaud/DevSkills OÜ/veebipood/multi-store/client/plugins/**/*.{js,ts,mjs}","C:/Users/<USER>/OneDrive/Töölaud/DevSkills OÜ/veebipood/multi-store/client/composables/**/*.{js,ts,mjs}","C:/Users/<USER>/OneDrive/Töölaud/DevSkills OÜ/veebipood/multi-store/client/utils/**/*.{js,ts,mjs}","C:/Users/<USER>/OneDrive/Töölaud/DevSkills OÜ/veebipood/multi-store/client/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Töölaud/DevSkills OÜ/veebipood/multi-store/client/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Töölaud/DevSkills OÜ/veebipood/multi-store/client/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Töölaud/DevSkills OÜ/veebipood/multi-store/client/app.config.{js,ts,mjs}"]}},
{},
cfg2
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;