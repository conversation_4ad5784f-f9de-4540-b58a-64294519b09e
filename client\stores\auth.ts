import { defineStore } from "pinia";

interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: "USER" | "ADMIN" | "SUPERADMIN";
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
}

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

interface AuthResponse {
  user: User;
  token: string;
}

interface ProfileData {
  firstName?: string;
  lastName?: string;
  email?: string;
}

interface PasswordData {
  currentPassword: string;
  newPassword: string;
}

export const useAuthStore = defineStore("auth", {
  state: (): AuthState => ({
    user: null,
    token: null,
    isAuthenticated: false,
    loading: false,
  }),

  getters: {
    isAdmin: (state): boolean =>
      state.user?.role === "ADMIN" || state.user?.role === "SUPERADMIN",
    isSuperAdmin: (state): boolean => state.user?.role === "SUPERADMIN",
    fullName: (state): string => {
      if (!state.user) return "";
      return `${state.user.firstName || ""} ${
        state.user.lastName || ""
      }`.trim();
    },
  },

  actions: {
    async login(credentials: LoginCredentials): Promise<AuthResponse> {
      this.loading = true;
      try {
        const api = useApi();
        const response = await api.post<AuthResponse>(
          "/auth/login",
          credentials,
          {
            showLoading: true,
            showSuccess: true,
            successMessage: "Login successful!",
            loadingMessage: "Signing in...",
          }
        );

        this.user = response.user;
        this.token = response.token;
        this.isAuthenticated = true;

        // Set token for future requests
        this.setAuthHeader();

        return response;
      } catch (error: unknown) {
        console.error("Login error:", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async register(userData: RegisterData): Promise<AuthResponse> {
      this.loading = true;
      try {
        const api = useApi();
        const response = await api.post<AuthResponse>(
          "/auth/register",
          userData,
          {
            showLoading: true,
            showSuccess: true,
            successMessage: "Registration successful!",
            loadingMessage: "Creating account...",
          }
        );

        this.user = response.user;
        this.token = response.token;
        this.isAuthenticated = true;

        // Set token for future requests
        this.setAuthHeader();

        return response;
      } catch (error: unknown) {
        console.error("Registration error:", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async logout(): Promise<void> {
      this.user = null;
      this.token = null;
      this.isAuthenticated = false;

      // Clear auth header
      this.clearAuthHeader();

      // Redirect to home page
      await navigateTo("/");
    },

    async fetchProfile(): Promise<void> {
      if (!this.token) return;

      try {
        const api = useApi();
        const response = await api.get<{ user: User }>("/auth/me");
        this.user = response.user;
        this.isAuthenticated = true;
      } catch (error: unknown) {
        console.error("Fetch profile error:", error);
        // If token is invalid, logout
        this.logout();
      }
    },

    async updateProfile(profileData: ProfileData): Promise<{ user: User }> {
      this.loading = true;
      try {
        const api = useApi();
        const response = await api.put<{ user: User }>(
          "/auth/profile",
          profileData,
          {
            showSuccess: true,
            successMessage: "Profile updated successfully!",
          }
        );

        this.user = response.user;
        return response;
      } catch (error: unknown) {
        console.error("Update profile error:", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async changePassword(passwordData: PasswordData): Promise<any> {
      this.loading = true;
      try {
        const api = useApi();
        const response = await api.post("/auth/change-password", passwordData, {
          showSuccess: true,
          successMessage: "Password changed successfully!",
        });

        return response;
      } catch (error: unknown) {
        console.error("Change password error:", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    setAuthHeader(): void {
      // No longer needed since we use $fetch with headers directly
      // Token is stored in the store and used in individual requests
    },

    clearAuthHeader(): void {
      // No longer needed since we use $fetch with headers directly
    },

    // Initialize auth state from persisted data
    initializeAuth(): void {
      if (this.token) {
        this.setAuthHeader();
        this.fetchProfile();
      }
    },
  },

  persist: {
    pick: ["user", "token", "isAuthenticated"],
  },
});
