<template>
  <div class="space-y-6">
    <!-- Section Header -->
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold">Page Section Backgrounds</h3>
      <button @click="resetToDefaults" class="btn btn-sm btn-outline">
        Reset to Defaults
      </button>
    </div>

    <p class="text-sm text-base-content/70">
      Customize the background colors for each section of your homepage. Changes will be visible on the landing page.
    </p>

    <!-- Section Background Controls -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      
      <!-- Hero Section -->
      <div class="card bg-base-100 border border-base-300 p-4">
        <h4 class="font-medium mb-3 flex items-center gap-2">
          <Icon name="heroicons:photo" class="h-4 w-4" />
          Hero Section
        </h4>
        <div class="form-control">
          <label class="label">
            <span class="label-text text-sm">Background Color</span>
          </label>
          <input
            v-model="localDesign.hero"
            type="color"
            class="input input-bordered h-12"
            @input="updateDesign"
          />
          <div class="label">
            <span class="label-text-alt text-xs">{{ localDesign.hero }}</span>
          </div>
        </div>
      </div>

      <!-- Benefit Cards Section -->
      <div class="card bg-base-100 border border-base-300 p-4">
        <h4 class="font-medium mb-3 flex items-center gap-2">
          <Icon name="heroicons:gift" class="h-4 w-4" />
          Benefit Cards
        </h4>
        <div class="form-control">
          <label class="label">
            <span class="label-text text-sm">Background Color</span>
          </label>
          <input
            v-model="localDesign.benefits"
            type="color"
            class="input input-bordered h-12"
            @input="updateDesign"
          />
          <div class="label">
            <span class="label-text-alt text-xs">{{ localDesign.benefits }}</span>
          </div>
        </div>
      </div>

      <!-- Trending Products Section -->
      <div class="card bg-base-100 border border-base-300 p-4">
        <h4 class="font-medium mb-3 flex items-center gap-2">
          <Icon name="heroicons:fire" class="h-4 w-4" />
          Trending Products
        </h4>
        <div class="form-control">
          <label class="label">
            <span class="label-text text-sm">Background Color</span>
          </label>
          <input
            v-model="localDesign.trending"
            type="color"
            class="input input-bordered h-12"
            @input="updateDesign"
          />
          <div class="label">
            <span class="label-text-alt text-xs">{{ localDesign.trending }}</span>
          </div>
        </div>
      </div>

      <!-- Categories Section -->
      <div class="card bg-base-100 border border-base-300 p-4">
        <h4 class="font-medium mb-3 flex items-center gap-2">
          <Icon name="heroicons:squares-2x2" class="h-4 w-4" />
          Categories
        </h4>
        <div class="form-control">
          <label class="label">
            <span class="label-text text-sm">Background Color</span>
          </label>
          <input
            v-model="localDesign.categories"
            type="color"
            class="input input-bordered h-12"
            @input="updateDesign"
          />
          <div class="label">
            <span class="label-text-alt text-xs">{{ localDesign.categories }}</span>
          </div>
        </div>
      </div>

      <!-- Limited Time Offer Section -->
      <div class="card bg-base-100 border border-base-300 p-4">
        <h4 class="font-medium mb-3 flex items-center gap-2">
          <Icon name="heroicons:clock" class="h-4 w-4" />
          Limited Time Offer
        </h4>
        <div class="form-control">
          <label class="label">
            <span class="label-text text-sm">Background</span>
          </label>
          <div class="space-y-2">
            <div class="flex gap-2">
              <label class="label cursor-pointer">
                <input
                  v-model="offerBackgroundType"
                  type="radio"
                  value="color"
                  class="radio radio-sm"
                  @change="updateOfferBackground"
                />
                <span class="label-text ml-2 text-sm">Solid Color</span>
              </label>
              <label class="label cursor-pointer">
                <input
                  v-model="offerBackgroundType"
                  type="radio"
                  value="gradient"
                  class="radio radio-sm"
                  @change="updateOfferBackground"
                />
                <span class="label-text ml-2 text-sm">Gradient</span>
              </label>
            </div>
            
            <input
              v-if="offerBackgroundType === 'color'"
              v-model="offerSolidColor"
              type="color"
              class="input input-bordered h-12"
              @input="updateOfferBackground"
            />
            
            <div v-else class="grid grid-cols-2 gap-2">
              <input
                v-model="offerGradientFrom"
                type="color"
                class="input input-bordered h-12"
                @input="updateOfferBackground"
              />
              <input
                v-model="offerGradientTo"
                type="color"
                class="input input-bordered h-12"
                @input="updateOfferBackground"
              />
            </div>
          </div>
          <div class="label">
            <span class="label-text-alt text-xs">{{ localDesign.limitedOffer }}</span>
          </div>
        </div>
      </div>

      <!-- New Arrivals Section -->
      <div class="card bg-base-100 border border-base-300 p-4">
        <h4 class="font-medium mb-3 flex items-center gap-2">
          <Icon name="heroicons:sparkles" class="h-4 w-4" />
          New Arrivals
        </h4>
        <div class="form-control">
          <label class="label">
            <span class="label-text text-sm">Background Color</span>
          </label>
          <input
            v-model="localDesign.newArrivals"
            type="color"
            class="input input-bordered h-12"
            @input="updateDesign"
          />
          <div class="label">
            <span class="label-text-alt text-xs">{{ localDesign.newArrivals }}</span>
          </div>
        </div>
      </div>

      <!-- Newsletter Section -->
      <div class="card bg-base-100 border border-base-300 p-4">
        <h4 class="font-medium mb-3 flex items-center gap-2">
          <Icon name="heroicons:envelope" class="h-4 w-4" />
          Newsletter
        </h4>
        <div class="form-control">
          <label class="label">
            <span class="label-text text-sm">Background Color</span>
          </label>
          <input
            v-model="localDesign.newsletter"
            type="color"
            class="input input-bordered h-12"
            @input="updateDesign"
          />
          <div class="label">
            <span class="label-text-alt text-xs">{{ localDesign.newsletter }}</span>
          </div>
        </div>
      </div>

    </div>

    <!-- Preview Section -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-medium mb-3">Preview</h4>
      <p class="text-sm text-base-content/70 mb-4">
        Section backgrounds will be applied to the landing page. Visit the homepage to see the full effect.
      </p>
      
      <!-- Mini Preview -->
      <div class="space-y-2">
        <div class="h-8 rounded flex items-center px-3 text-xs font-medium" :style="{ backgroundColor: localDesign.hero }">
          Hero Section
        </div>
        <div class="h-8 rounded flex items-center px-3 text-xs font-medium" :style="{ backgroundColor: localDesign.benefits }">
          Benefit Cards
        </div>
        <div class="h-8 rounded flex items-center px-3 text-xs font-medium" :style="{ backgroundColor: localDesign.trending }">
          Trending Products
        </div>
        <div class="h-8 rounded flex items-center px-3 text-xs font-medium" :style="{ backgroundColor: localDesign.categories }">
          Categories
        </div>
        <div class="h-8 rounded flex items-center px-3 text-xs font-medium text-white" :style="{ background: localDesign.limitedOffer }">
          Limited Time Offer
        </div>
        <div class="h-8 rounded flex items-center px-3 text-xs font-medium" :style="{ backgroundColor: localDesign.newArrivals }">
          New Arrivals
        </div>
        <div class="h-8 rounded flex items-center px-3 text-xs font-medium" :style="{ backgroundColor: localDesign.newsletter }">
          Newsletter
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Default design settings
const getDefaultDesign = () => ({
  hero: "transparent",
  benefits: "#f8fafc",
  trending: "#f1f5f9",
  categories: "#f8fafc",
  limitedOffer: "linear-gradient(to right, #3b82f6, #8b5cf6)",
  newArrivals: "#f1f5f9",
  newsletter: "#f8fafc",
});

// Props
const props = defineProps({
  design: {
    type: Object,
    default: () => getDefaultDesign(),
  },
});

// Emits
const emit = defineEmits(["update:design", "update"]);

// Local design state
const localDesign = ref({ ...getDefaultDesign(), ...props.design });

// Limited offer background controls
const offerBackgroundType = ref("gradient");
const offerSolidColor = ref("#3b82f6");
const offerGradientFrom = ref("#3b82f6");
const offerGradientTo = ref("#8b5cf6");

// Initialize offer background controls
const initializeOfferControls = () => {
  const offer = localDesign.value.limitedOffer;
  if (offer.startsWith("linear-gradient")) {
    offerBackgroundType.value = "gradient";
    // Extract colors from gradient (simplified)
    const matches = offer.match(/#[0-9a-fA-F]{6}/g);
    if (matches && matches.length >= 2) {
      offerGradientFrom.value = matches[0];
      offerGradientTo.value = matches[1];
    }
  } else {
    offerBackgroundType.value = "color";
    offerSolidColor.value = offer;
  }
};

// Initialize on mount
onMounted(() => {
  initializeOfferControls();
});

// Watch for prop changes
watch(
  () => props.design,
  (newDesign) => {
    localDesign.value = { ...getDefaultDesign(), ...newDesign };
    initializeOfferControls();
  },
  { deep: true }
);

// Update design
const updateDesign = () => {
  emit("update:design", { ...localDesign.value });
  emit("update");
};

// Update offer background
const updateOfferBackground = () => {
  if (offerBackgroundType.value === "color") {
    localDesign.value.limitedOffer = offerSolidColor.value;
  } else {
    localDesign.value.limitedOffer = `linear-gradient(to right, ${offerGradientFrom.value}, ${offerGradientTo.value})`;
  }
  updateDesign();
};

// Reset to defaults
const resetToDefaults = () => {
  localDesign.value = getDefaultDesign();
  initializeOfferControls();
  updateDesign();
};
</script>
